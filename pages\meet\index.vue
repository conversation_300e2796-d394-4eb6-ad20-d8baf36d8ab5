<template>
  <div class="meet-page">
    <v-container fluid class="pa-4">
      <v-row>
        <v-col cols="12">
          <div class="meet-header mb-4">
            <h1 class="text-h4 font-weight-bold mb-2">Video Call Test</h1>
            <p class="text-subtitle-1 grey--text">
              Test the Whereby video call integration
            </p>
          </div>
        </v-col>
      </v-row>

      <v-row>
        <v-col cols="12" md="8">
          <v-card class="meet-video-card" elevation="2">
            <v-card-title class="d-flex align-center justify-space-between">
              <span>Video Call</span>
              <div class="video-provider-buttons">
                <v-btn
                  :class="['mr-2', { primary: currentProvider === 'twilio' }]"
                  small
                  @click="switchProvider('twilio')"
                >
                  A - Twilio
                </v-btn>
                <v-btn
                  :class="['mr-2', { primary: currentProvider === 'tokbox' }]"
                  small
                  @click="switchProvider('tokbox')"
                >
                  B - Tokbox
                </v-btn>
                <v-btn
                  :class="[{ primary: currentProvider === 'whereby' }]"
                  small
                  @click="switchProvider('whereby')"
                >
                  C - Whereby
                </v-btn>
              </div>
            </v-card-title>
            <v-card-text class="pa-0">
              <div
                class="video-container"
                style="height: 500px; position: relative"
              >
                <!-- Whereby Video Component -->
                <div
                  v-if="currentProvider === 'whereby'"
                  id="whereby-video-container"
                  class="whereby-video-container"
                  style="width: 100%; height: 100%"
                ></div>

                <!-- Placeholder for other providers -->
                <div
                  v-else
                  class="d-flex align-center justify-center"
                  style="height: 100%; background: #f5f5f5"
                >
                  <div class="text-center">
                    <v-icon size="64" color="grey">mdi-video-off</v-icon>
                    <p class="text-h6 mt-2 grey--text">
                      {{ currentProvider === 'twilio' ? 'Twilio' : 'Tokbox' }}
                      Provider Selected
                    </p>
                    <p class="grey--text">
                      Video integration not implemented for testing
                    </p>
                  </div>
                </div>

                <!-- Video Controls -->
                <div
                  v-if="currentProvider === 'whereby'"
                  class="video-controls"
                  style="
                    position: absolute;
                    bottom: 16px;
                    left: 16px;
                    right: 16px;
                  "
                >
                  <v-card class="pa-2" style="background: rgba(0, 0, 0, 0.7)">
                    <div class="d-flex align-center justify-center">
                      <v-btn
                        :color="isVideoEnabled ? 'success' : 'error'"
                        icon
                        @click="toggleVideo"
                      >
                        <v-icon>{{
                          isVideoEnabled ? 'mdi-video' : 'mdi-video-off'
                        }}</v-icon>
                      </v-btn>
                      <v-btn
                        :color="!isMuted ? 'success' : 'error'"
                        icon
                        class="mx-2"
                        @click="toggleAudio"
                      >
                        <v-icon>{{
                          !isMuted ? 'mdi-microphone' : 'mdi-microphone-off'
                        }}</v-icon>
                      </v-btn>
                      <v-btn color="primary" icon @click="toggleFullScreen">
                        <v-icon>mdi-fullscreen</v-icon>
                      </v-btn>
                    </div>
                  </v-card>
                </div>
              </div>
            </v-card-text>
          </v-card>
        </v-col>

        <v-col cols="12" md="4">
          <v-card elevation="2">
            <v-card-title>Connection Info</v-card-title>
            <v-card-text>
              <div class="mb-3">
                <strong>Provider:</strong> {{ currentProvider.toUpperCase() }}
              </div>
              <div class="mb-3">
                <strong>Status:</strong>
                <v-chip
                  :color="isConnected ? 'success' : 'warning'"
                  small
                  text-color="white"
                >
                  {{ isConnected ? 'Connected' : 'Connecting...' }}
                </v-chip>
              </div>
              <div class="mb-3"><strong>Room:</strong> {{ roomName }}</div>
              <div class="mb-3">
                <strong>Video:</strong> {{ isVideoEnabled ? 'On' : 'Off' }}
              </div>
              <div class="mb-3">
                <strong>Audio:</strong> {{ !isMuted ? 'On' : 'Off' }}
              </div>
            </v-card-text>
          </v-card>

          <v-card class="mt-4" elevation="2">
            <v-card-title>Instructions</v-card-title>
            <v-card-text>
              <ol class="pl-4">
                <li>
                  Click on provider buttons (A, B, C) to switch between video
                  providers
                </li>
                <li>Whereby (C) is fully functional for testing</li>
                <li>
                  Use video controls to toggle camera, microphone, and
                  fullscreen
                </li>
                <li>Multiple users can join the same room for testing</li>
              </ol>
            </v-card-text>
          </v-card>
        </v-col>
      </v-row>
    </v-container>
  </div>
</template>

<script>
export default {
  name: 'MeetPage',
  data() {
    return {
      currentProvider: 'whereby',
      isConnected: false,
      isVideoEnabled: true,
      isMuted: false,
      roomName: 'test-meet-room',
      wherebyClient: null,
      localStreamContainer: null,
    }
  },
  head() {
    return {
      title: 'Video Call Test - Langu',
      meta: [
        {
          hid: 'description',
          name: 'description',
          content: 'Test video call functionality with Whereby integration',
        },
      ],
    }
  },
  mounted() {
    this.$nextTick(() => {
      this.localStreamContainer = document.getElementById(
        'whereby-video-container'
      )
      if (this.currentProvider === 'whereby') {
        this.initializeWhereby()
      }
    })

    window.addEventListener('beforeunload', this.cleanup)
  },
  beforeDestroy() {
    this.cleanup()
  },
  methods: {
    switchProvider(provider) {
      this.cleanup()
      this.currentProvider = provider
      this.isConnected = false

      if (provider === 'whereby') {
        this.$nextTick(() => {
          this.localStreamContainer = document.getElementById(
            'whereby-video-container'
          )
          this.initializeWhereby()
        })
      }
    },
    initializeWhereby() {
      try {
        // For CSP compliance, we need to open Whereby in a new window instead of iframe
        // The CSP error indicates that Whereby doesn't allow embedding from localhost

        // Create a button to create and open Whereby room
        const container = this.localStreamContainer
        if (container) {
          container.innerHTML = `
            <div style="display: flex; flex-direction: column; align-items: center; justify-content: center; height: 100%; background: #f5f5f5; text-align: center;">
              <div style="margin-bottom: 20px;">
                <h3 style="color: #333; margin-bottom: 10px;">Whereby Video Call</h3>
                <p style="color: #666; margin-bottom: 20px;">Click to create and join a new video room</p>
              </div>
              <button
                id="whereby-open-btn"
                style="
                  background: #5E72E4;
                  color: white;
                  border: none;
                  padding: 12px 24px;
                  border-radius: 6px;
                  font-size: 16px;
                  cursor: pointer;
                  margin-bottom: 10px;
                "
                onmouseover="this.style.background='#4C63D2'"
                onmouseout="this.style.background='#5E72E4'"
              >
                Create & Open Whereby Room
              </button>
              <p style="color: #888; font-size: 14px;" id="room-status">Ready to create room...</p>
            </div>
          `

          // Add click handler to create and open Whereby room
          const openBtn = container.querySelector('#whereby-open-btn')
          const statusEl = container.querySelector('#room-status')

          if (openBtn && statusEl) {
            openBtn.addEventListener('click', async () => {
              openBtn.disabled = true
              openBtn.textContent = 'Creating Room...'
              statusEl.textContent = 'Creating Whereby room...'

              try {
                const roomUrl = await this.createWherebyRoom()
                if (roomUrl) {
                  statusEl.textContent = `Room created: ${roomUrl
                    .split('/')
                    .pop()}`

                  const embedParams = `?embed&displayName=Test User&audio=${
                    !this.isMuted ? 'on' : 'off'
                  }&video=${
                    this.isVideoEnabled ? 'on' : 'off'
                  }&chat=off&people=off&leaveButton=off&background=off`

                  window.open(
                    roomUrl + embedParams,
                    '_blank',
                    'width=1200,height=800,scrollbars=yes,resizable=yes'
                  )
                  this.isConnected = true
                  openBtn.textContent = 'Room Opened!'
                } else {
                  throw new Error('Failed to create room')
                }
              } catch (error) {
                // eslint-disable-next-line no-console
                console.error('Failed to create Whereby room:', error)
                statusEl.textContent =
                  'Failed to create room. Please try again.'
                openBtn.textContent = 'Create & Open Whereby Room'
                openBtn.disabled = false
              }
            })
          }
        }
      } catch (error) {
        // eslint-disable-next-line no-console
        console.error('Failed to initialize Whereby:', error)
        this.isConnected = false
      }
    },

    async createWherebyRoom() {
      try {
        // Use Whereby REST API to create a new room
        const wherebyToken =
          'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**********************************************************************************************************************************************************************************************************************************************.1dZgX4paCsT4jgcW7ueteClMW8ZzlASc8j1LphFja8U'

        const response = await this.$axios.post(
          'https://api.whereby.dev/v1/meetings',
          {
            endDate: new Date(Date.now() + 2 * 60 * 60 * 1000).toISOString(), // 2 hours from now
            roomNamePrefix: 'langu-meet',
            roomMode: 'normal',
            isLocked: false,
          },
          {
            headers: {
              Authorization: `Bearer ${wherebyToken}`,
              'Content-Type': 'application/json',
            },
          }
        )

        if (response.data && response.data.roomUrl) {
          return response.data.roomUrl
        }

        throw new Error('No room URL in response')
      } catch (error) {
        // eslint-disable-next-line no-console
        console.error('Whereby API error:', error)

        // Fallback: Try to use a simple room name approach
        const fallbackRoomName = `langu-meet-${Date.now()}`
        return `https://whereby.com/${fallbackRoomName}`
      }
    },

    toggleVideo() {
      this.isVideoEnabled = !this.isVideoEnabled
      // Note: With iframe approach, video controls are handled within Whereby interface
      // eslint-disable-next-line no-console
      console.log('Video toggled:', this.isVideoEnabled)
    },
    toggleAudio() {
      this.isMuted = !this.isMuted
      // Note: With iframe approach, audio controls are handled within Whereby interface
      // eslint-disable-next-line no-console
      console.log('Audio toggled:', !this.isMuted)
    },
    toggleFullScreen() {
      if (this.localStreamContainer) {
        if (document.fullscreenElement) {
          document.exitFullscreen()
        } else {
          this.localStreamContainer.requestFullscreen()
        }
      }
    },
    cleanup() {
      if (this.localStreamContainer) {
        this.localStreamContainer.innerHTML = ''
      }
      this.isConnected = false
    },
  },
}
</script>

<style lang="scss" scoped>
.meet-page {
  min-height: 100vh;
  background: #f5f5f5;
}

.meet-header {
  text-align: center;
}

.meet-video-card {
  .video-container {
    background: #000;
    border-radius: 8px;
    overflow: hidden;
  }

  .whereby-video-container {
    background: #000;
  }
}

.video-provider-buttons {
  .v-btn {
    min-width: auto;
  }
}

.video-controls {
  .v-btn {
    color: white !important;
  }
}
</style>
