<template>
  <div class="meet-page">
    <v-container fluid class="pa-4">
      <v-row>
        <v-col cols="12">
          <div class="meet-header mb-4">
            <h1 class="text-h4 font-weight-bold mb-2">Video Call Test</h1>
            <p class="text-subtitle-1 grey--text">
              Test the Whereby video call integration
            </p>
          </div>
        </v-col>
      </v-row>

      <v-row>
        <v-col cols="12" md="8">
          <v-card class="meet-video-card" elevation="2">
            <v-card-title class="d-flex align-center justify-space-between">
              <span>Video Call</span>
              <div class="video-provider-buttons">
                <v-btn
                  :class="['mr-2', { primary: currentProvider === 'twilio' }]"
                  small
                  @click="switchProvider('twilio')"
                >
                  A - Twilio
                </v-btn>
                <v-btn
                  :class="['mr-2', { primary: currentProvider === 'tokbox' }]"
                  small
                  @click="switchProvider('tokbox')"
                >
                  B - Tokbox
                </v-btn>
                <v-btn
                  :class="[{ primary: currentProvider === 'whereby' }]"
                  small
                  @click="switchProvider('whereby')"
                >
                  C - Whereby
                </v-btn>
              </div>
            </v-card-title>
            <v-card-text class="pa-0">
              <div
                class="video-container"
                style="height: 500px; position: relative"
              >
                <!-- Whereby Video Component -->
                <div
                  v-if="currentProvider === 'whereby'"
                  id="whereby-video-container"
                  class="whereby-video-container"
                  style="width: 100%; height: 100%"
                ></div>

                <!-- Placeholder for other providers -->
                <div
                  v-else
                  class="d-flex align-center justify-center"
                  style="height: 100%; background: #f5f5f5"
                >
                  <div class="text-center">
                    <v-icon size="64" color="grey">mdi-video-off</v-icon>
                    <p class="text-h6 mt-2 grey--text">
                      {{ currentProvider === 'twilio' ? 'Twilio' : 'Tokbox' }}
                      Provider Selected
                    </p>
                    <p class="grey--text">
                      Video integration not implemented for testing
                    </p>
                  </div>
                </div>

                <!-- Video Controls -->
                <div
                  v-if="currentProvider === 'whereby'"
                  class="video-controls"
                  style="
                    position: absolute;
                    bottom: 16px;
                    left: 16px;
                    right: 16px;
                  "
                >
                  <v-card class="pa-2" style="background: rgba(0, 0, 0, 0.7)">
                    <div class="d-flex align-center justify-center">
                      <v-btn
                        :color="isVideoEnabled ? 'success' : 'error'"
                        icon
                        @click="toggleVideo"
                      >
                        <v-icon>{{
                          isVideoEnabled ? 'mdi-video' : 'mdi-video-off'
                        }}</v-icon>
                      </v-btn>
                      <v-btn
                        :color="!isMuted ? 'success' : 'error'"
                        icon
                        class="mx-2"
                        @click="toggleAudio"
                      >
                        <v-icon>{{
                          !isMuted ? 'mdi-microphone' : 'mdi-microphone-off'
                        }}</v-icon>
                      </v-btn>
                      <v-btn color="primary" icon @click="toggleFullScreen">
                        <v-icon>mdi-fullscreen</v-icon>
                      </v-btn>
                    </div>
                  </v-card>
                </div>
              </div>
            </v-card-text>
          </v-card>
        </v-col>

        <v-col cols="12" md="4">
          <v-card elevation="2">
            <v-card-title>Connection Info</v-card-title>
            <v-card-text>
              <div class="mb-3">
                <strong>Provider:</strong> {{ currentProvider.toUpperCase() }}
              </div>
              <div class="mb-3">
                <strong>Status:</strong>
                <v-chip
                  :color="isConnected ? 'success' : 'warning'"
                  small
                  text-color="white"
                >
                  {{ isConnected ? 'Connected' : 'Connecting...' }}
                </v-chip>
              </div>
              <div class="mb-3"><strong>Room:</strong> {{ roomName }}</div>
              <div class="mb-3">
                <strong>Video:</strong> {{ isVideoEnabled ? 'On' : 'Off' }}
              </div>
              <div class="mb-3">
                <strong>Audio:</strong> {{ !isMuted ? 'On' : 'Off' }}
              </div>
            </v-card-text>
          </v-card>

          <v-card class="mt-4" elevation="2">
            <v-card-title>Instructions</v-card-title>
            <v-card-text>
              <ol class="pl-4">
                <li>
                  Click on provider buttons (A, B, C) to switch between video
                  providers
                </li>
                <li>Whereby (C) is fully functional for testing</li>
                <li>
                  Use video controls to toggle camera, microphone, and
                  fullscreen
                </li>
                <li>Multiple users can join the same room for testing</li>
              </ol>
            </v-card-text>
          </v-card>
        </v-col>
      </v-row>
    </v-container>
  </div>
</template>

<script>
export default {
  name: 'MeetPage',
  data() {
    return {
      currentProvider: 'whereby',
      isConnected: false,
      isVideoEnabled: true,
      isMuted: false,
      roomName: 'test-meet-room',
      wherebyClient: null,
      localStreamContainer: null,
    }
  },
  head() {
    return {
      title: 'Video Call Test - Langu',
      meta: [
        {
          hid: 'description',
          name: 'description',
          content: 'Test video call functionality with Whereby integration',
        },
      ],
    }
  },
  mounted() {
    this.$nextTick(() => {
      this.localStreamContainer = document.getElementById(
        'whereby-video-container'
      )
      if (this.currentProvider === 'whereby') {
        this.initializeWhereby()
      }
    })

    window.addEventListener('beforeunload', this.cleanup)
  },
  beforeDestroy() {
    this.cleanup()
  },
  methods: {
    switchProvider(provider) {
      this.cleanup()
      this.currentProvider = provider
      this.isConnected = false

      if (provider === 'whereby') {
        this.$nextTick(() => {
          this.localStreamContainer = document.getElementById(
            'whereby-video-container'
          )
          this.initializeWhereby()
        })
      }
    },
    initializeWhereby() {
      try {
        // Use your manually created room from Whereby dashboard
        const roomUrl =
          'https://langu.whereby.com/room-27285779be043c-54d6-4c7c-8449-5091e3a3301f'

        // Create iframe for Whereby embedded
        const iframe = document.createElement('iframe')
        iframe.src = `${roomUrl}?embed&displayName=Test User&audio=${!this
          .isMuted}&video=${
          this.isVideoEnabled
        }&chat=off&people=off&leaveButton=off&background=off`
        iframe.style.width = '100%'
        iframe.style.height = '100%'
        iframe.style.border = 'none'
        iframe.allow = 'camera; microphone; fullscreen; display-capture'
        iframe.allowFullscreen = true

        // Clear container and add iframe
        if (this.localStreamContainer) {
          this.localStreamContainer.innerHTML = ''
          this.localStreamContainer.appendChild(iframe)
        }

        // Mark as connected after a short delay
        setTimeout(() => {
          this.isConnected = true
        }, 1000)
      } catch (error) {
        console.error('Failed to initialize Whereby:', error)
        this.isConnected = false
      }
    },
    toggleVideo() {
      this.isVideoEnabled = !this.isVideoEnabled
      // Note: With iframe approach, video controls are handled within Whereby interface
      console.log('Video toggled:', this.isVideoEnabled)
    },
    toggleAudio() {
      this.isMuted = !this.isMuted
      // Note: With iframe approach, audio controls are handled within Whereby interface
      console.log('Audio toggled:', !this.isMuted)
    },
    toggleFullScreen() {
      if (this.localStreamContainer) {
        if (document.fullscreenElement) {
          document.exitFullscreen()
        } else {
          this.localStreamContainer.requestFullscreen()
        }
      }
    },
    cleanup() {
      if (this.localStreamContainer) {
        this.localStreamContainer.innerHTML = ''
      }
      this.isConnected = false
    },
  },
}
</script>

<style lang="scss" scoped>
.meet-page {
  min-height: 100vh;
  background: #f5f5f5;
}

.meet-header {
  text-align: center;
}

.meet-video-card {
  .video-container {
    background: #000;
    border-radius: 8px;
    overflow: hidden;
  }

  .whereby-video-container {
    background: #000;
  }
}

.video-provider-buttons {
  .v-btn {
    min-width: auto;
  }
}

.video-controls {
  .v-btn {
    color: white !important;
  }
}
</style>
