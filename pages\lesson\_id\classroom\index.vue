<template>
  <div
    v-if="zoomAsset"
    id="main-component"
    class="classroom"
    @click="userInteracted"
    @wheel="onwheel"
    @gesturestart.prevent="gestureStart"
    @gesturechange.prevent="gestureChange"
    @gestureend.prevent="gestureEnd"
    @mouseup="onUp"
    @mouseleave="onUp"
    @mousemove="mouseMoved"
  >
    <drop-file-area :viewport-width="viewportWidth"></drop-file-area>
    <div v-show="!isDragging">
      <div v-if="timer" class="classroom-timer" :style="timeRemainingStyle">
        {{ $t('time_remaining') }}: {{ timer }}
      </div>
      <div
        :class="['zoom-percent', { 'zoom-percent--show': zoomPercent !== 100 }]"
      >
        {{ $t('zoom') }}: {{ zoomPercent }}%
      </div>

      <viewport
        :zoom-other-asset="zoomOtherAsset"
        :zoom-asset="zoomAsset"
        :viewport-width="viewportWidth"
        :viewport-height="viewportHeight"
      ></viewport>

      <other-cursor></other-cursor>

      <tinymce-vue
        v-if="editorAsset"
        :file="editorAsset"
        @mouse-move="mouseMoved"
        @pointer-down="pointerDownHandler($event)"
        @pointer-move="pointerMoveHandler($event)"
        @pointer-up="pointerUpHandler($event)"
      ></tinymce-vue>
      <video-item
        v-for="videoAsset in videoAssets"
        :key="videoAsset.id"
        :file="videoAsset"
      ></video-item>
      <audio-item
        v-for="audioAsset in audioAssets"
        :key="audioAsset.id"
        :file="audioAsset"
      ></audio-item>
      <pdf-item
        v-for="pdfAsset in pdfAssets"
        :key="pdfAsset.id"
        :file="pdfAsset"
      ></pdf-item>
      <image-item
        v-for="imageAsset in imageAssets"
        :key="imageAsset.id"
        :file="imageAsset"
      ></image-item>

      <twilio
        v-if="twilioAsset && screenShareAsset"
        :file="twilioAsset"
        :screen-share-asset="screenShareAsset"
        :zoom-other-asset="zoomOtherAsset"
      ></twilio>
      <tokbox
        v-if="tokboxAsset && screenShareAsset"
        :file="tokboxAsset"
        :screen-share-asset="screenShareAsset"
        :zoom-other-asset="zoomOtherAsset"
      ></tokbox>
      <whereby
        v-if="wherebyAsset && screenShareAsset"
        :file="wherebyAsset"
        :screen-share-asset="screenShareAsset"
        :zoom-other-asset="zoomOtherAsset"
      ></whereby>

      <l-dialog
        custom-class="video-component"
        max-width="500"
        z-index="10000000001"
        :dialog="isVideoInputOpened"
        @close-dialog="closeVideoInputDialog"
      >
        <video-input></video-input>
      </l-dialog>

      <library v-if="isLibraryOpened" :viewport-width="viewportWidth"></library>

      <toolbar
        :file="lockAsset"
        :student-id="studentId"
        :scale="zoomIndex"
        :min-zoom="minZoom"
        :viewport-width="viewportWidth"
        :viewport-height="viewportHeight"
        :is-finished-allowed="isFinishedAllowed"
      ></toolbar>
      <div
        id="konva"
        @mousedown="konvaMouseDownHandler"
        @mouseup="konvaMouseUpHandler"
      >
        <konva
          v-if="shapeAsset"
          :scale="zoomIndex"
          :file="shapeAsset"
          :width="viewportWidth"
          :height="viewportHeight"
          is-main-konva
        ></konva>
      </div>
    </div>
  </div>
</template>

<script>
import { debounce } from '~/helpers'

import DropFileArea from '~/components/classroom/DropFileArea'
import Viewport from '~/components/classroom/Viewport'
import OtherCursor from '~/components/classroom/OtherCursor'
import TinymceVue from '~/components/classroom/TinymceVue'
import VideoItem from '~/components/classroom/VideoItem'
import AudioItem from '~/components/classroom/AudioItem'
import PdfItem from '~/components/classroom/PdfItem'
import ImageItem from '~/components/classroom/ImageItem'
import Twilio from '~/components/classroom/video/Twilio'
import Tokbox from '~/components/classroom/video/Tokbox'
import Whereby from '~/components/classroom/video/Whereby'
import VideoInput from '~/components/classroom/VideoInput'
import Library from '~/components/classroom/Library'
import Toolbar from '~/components/classroom/Toolbar'
import Konva from '~/components/classroom/Konva'
import LDialog from '~/components/LDialog'

import { isIOS, isDevice } from '~/helpers/check_device'
import { addEvent, removeEvent } from '~/helpers/dom'

import {
  TOOL_POINTER,
  mainCanvasWidth,
  mainCanvasHeight,
  mainCanvasOffsetX,
  mainCanvasOffsetY,
} from '~/helpers/constants'
import SetTool from '~/mixins/SetTool'

export default {
  name: 'Classroom',
  components: {
    DropFileArea,
    Viewport,
    OtherCursor,
    TinymceVue,
    VideoItem,
    AudioItem,
    PdfItem,
    ImageItem,
    Twilio,
    Tokbox,
    Whereby,
    VideoInput,
    Library,
    Toolbar,
    Konva,
    LDialog,
  },
  mixins: [SetTool],
  layout: 'classroomLayout',
  async asyncData({ store, route, redirect }) {
    const lessonId = route.params.id

    await store
      .dispatch('classroom/getItem', lessonId)
      .then((data) => {
        store.commit('classroom/SET_LESSON_ID', lessonId)
        store.commit('classroom/SET_TEACHER_ID', data.teacherId.toString())
        store.commit('classroom/SET_STUDENT_ID', data.studentId.toString())
      })
      .catch((e) => {
        redirect('/user/lessons')
      })

    return { lessonId }
  },
  data() {
    return {
      isDevice: false,
      viewportWidth: 0,
      viewportHeight: 0,
      startScale: 1,
      mouseClickPosition: { x: 0, y: 0 },
      evCache: [],
      prevDiff: -1,
      pointerMoveTimeout: null,
      dragTimeout: null,
      mouseMoveTimeout: null,
      screenResizeTimeout: null,
      touchStatus: null,
      otherCursorPosition: { x: 0, y: 0 },
      timer: null,
      keysPressed: {},
      intervalId: null,
      isFinishedAllowed: false,
    }
  },
  computed: {
    classroom() {
      return this.$store.state.classroom.item
    },
    userId() {
      return this.$store.getters['classroom/userId']
    },
    otherUserRole() {
      return this.$store.getters['classroom/otherUserRole']
    },
    studentId() {
      return this.$store.state.classroom.studentId
    },
    userName() {
      return this.$store.getters['classroom/userName']
    },
    lessonStartTime() {
      return this.classroom.startDate
    },
    lessonDuration() {
      return this.classroom.length
    },
    isCanvasOversizeX() {
      return mainCanvasWidth > this.viewportWidth
    },
    isScaledCanvasOversizeX() {
      return mainCanvasWidth * this.zoomIndex > this.viewportWidth
    },
    isCanvasOversizeY() {
      return mainCanvasHeight > this.viewportHeight
    },
    isScaledCanvasOversizeY() {
      return mainCanvasHeight * this.zoomIndex > this.viewportHeight
    },
    shapeAsset() {
      return this.$store.state.classroom.assets.find(
        (asset) => asset.asset.type === 'shape'
      )
    },
    editorAsset() {
      return this.$store.state.classroom.assets.find(
        (asset) => asset.asset.type === 'editor'
      )
    },
    twilioAsset() {
      return this.$store.state.classroom.assets.find(
        (asset) => asset.asset.type === 'twilio'
      )
    },
    tokboxAsset() {
      return this.$store.state.classroom.assets.find(
        (asset) => asset.asset.type === 'tokbox'
      )
    },
    wherebyAsset() {
      return this.$store.state.classroom.assets.find(
        (asset) => asset.asset.type === 'whereby'
      )
    },
    imageAssets() {
      return this.$store.state.classroom.assets.filter(
        (asset) => asset.asset.type === 'image'
      )
    },
    pdfAssets() {
      return this.$store.state.classroom.assets.filter(
        (asset) => asset.asset.type === 'pdf'
      )
    },
    videoAssets() {
      return this.$store.state.classroom.assets.filter(
        (asset) => asset.asset.type === 'video'
      )
    },
    audioAssets() {
      return this.$store.getters['classroom/audioAssets']
    },
    lockAsset() {
      return this.$store.state.classroom.assets.find(
        (asset) => asset.asset.type === 'lock'
      )
    },
    screenShareAsset() {
      return this.$store.state.classroom.assets.find(
        (asset) => asset.asset.type === 'screenShare'
      )
    },
    isLocked() {
      return this.$store.getters['classroom/isLocked']
    },
    isVideoInputOpened() {
      return this.$store.state.classroom.isVideoInputOpened
    },
    isLibraryOpened() {
      return this.$store.state.classroom.isLibraryOpened
    },
    isDragging() {
      return this.$store.state.classroom.isDragging
    },
    userParams() {
      return this.$store.getters['classroom/userParams']
    },
    zoomAsset() {
      return this.$store.getters['classroom/zoomAsset']
    },
    zoomOtherAsset() {
      return this.$store.getters['classroom/otherZoomAsset']
    },
    zoomIndex() {
      return this.zoomAsset?.asset?.zoomIndex ?? 1
    },
    zoomPercent() {
      return Math.round(this.zoomIndex * 100)
    },
    minSizeX() {
      return mainCanvasOffsetX
    },
    minSizeY() {
      return mainCanvasOffsetY
    },
    maxSizeXPoint() {
      return mainCanvasWidth + mainCanvasOffsetX
    },
    maxSizeYPoint() {
      return mainCanvasHeight + mainCanvasOffsetY
    },
    maxSizeX() {
      return this.isScaledCanvasOversizeX
        ? this.maxSizeXPoint - window.innerWidth / this.zoomIndex
        : 0
    },
    maxSizeY() {
      return this.isScaledCanvasOversizeY
        ? this.maxSizeYPoint - window.innerHeight / this.zoomIndex
        : 0
    },
    minZoom() {
      return this.isCanvasOversizeX && this.isCanvasOversizeY
        ? Math.max(
            window.innerWidth / (this.maxSizeXPoint - this.minSizeX),
            window.innerHeight / (this.maxSizeYPoint - this.minSizeY)
          )
        : window.innerWidth / this.maxSizeXPoint
    },
    maxZoom() {
      return 2
    },
    lessonEndTime() {
      return this.$dayjs(this.lessonStartTime).add(
        this.lessonDuration,
        'minute'
      )
    },
    userTz() {
      return this.classroom.userTimezone
    },
    isScrollMainComponent() {
      return this.$store.state.classroom.isScrollMainComponent
    },
    timeRemainingStyle() {
      return {
        bottom: this.isScaledCanvasOversizeY
          ? '15px'
          : `${this.viewportHeight - mainCanvasHeight * this.zoomIndex + 15}px`,
        right: this.isScaledCanvasOversizeX
          ? '75px'
          : `${this.viewportWidth - mainCanvasWidth * this.zoomIndex + 75}px`,
      }
    },
    isUserInteracted() {
      return this.$store.state.classroom.isUserInteracted
    },
  },
  watch: {
    'userParams.cursor'(cursor) {
      this.toggleBodyClass('addClass', cursor)
    },
    isLocked(newValue, oldValue) {
      if (this.role === 'student') {
        const bodyEl = document.body
        const bodyClass = 'room-is-disabled'

        if (newValue) {
          this.setTool('pointer', 'cursor-pointer')
          bodyEl.classList.add(bodyClass)
        } else {
          bodyEl.className = bodyEl.className.replace(bodyClass, '')
        }
      }
    },
  },
  async mounted() {
    this.isDevice = isDevice()
    this.viewportWidth = window.innerWidth
    this.viewportHeight = window.innerHeight

    await this.$store.dispatch('classroom/getAssets', this.lessonId)

    this.intervalId = setInterval(() => {
      const currentTime = this.$dayjs().tz(this.userTz).format()
      const diff = this.$dayjs(this.lessonEndTime).diff(currentTime, 'second')

      if (diff > 0) {
        if (this.lessonDuration * 60 > diff) {
          let minutes = Math.abs(Math.floor(diff / 60)).toString()
          minutes = minutes.length < 2 ? `0${minutes}` : minutes

          let seconds = Math.abs(diff % 60).toString()
          seconds = seconds.length < 2 ? `0${seconds}` : seconds

          this.timer = `${minutes}:${seconds}`

          if (!this.isFinishedAllowed) {
            this.isFinishedAllowed = true
          }
        } else {
          this.timer = `${this.lessonDuration}:00`
        }
      } else {
        this.timer = '0:00'

        if (!this.isFinishedAllowed) {
          this.isFinishedAllowed = true
        }
      }
    }, 1000)

    this.updateScreenSize()

    this.toggleBodyClass('addClass', 'role')
    this.toggleBodyClass('addClass', this.userParams?.cursor)

    if (!this.editorAsset) {
      await this.$store.dispatch('classroom/createAsset', {
        type: 'editor',
      })
    }

    if (!this.shapeAsset) {
      await this.$store.dispatch('classroom/createAsset', {
        type: 'shape',
        shapes: [],
      })
    }

    if (!this.lockAsset) {
      await this.$store.dispatch('classroom/createAsset', {
        type: 'lock',
        isLocked: false,
      })
    }

    if (!this.twilioAsset && !this.tokboxAsset && !this.wherebyAsset) {
      await this.$store.dispatch('classroom/createAsset', {
        type: 'twilio',
        width: 400,
        height: 300,
        settings: {
          teacher: {
            isMuted: false,
            isVideoEnabled: true,
          },
          student: {
            isMuted: false,
            isVideoEnabled: true,
          },
        },
      })
    }

    if (!this.screenShareAsset) {
      await this.$store.dispatch('classroom/createAsset', {
        type: 'screenShare',
        top: 100,
        left: this.viewportWidth / 2 - 200,
        width: 400,
        height: 300,
      })
    }

    if (!this.zoomAsset) {
      await this.$store.dispatch('classroom/createAsset', {
        type: 'zoom',
        user_id: this.userId,
        zoomIndex: this.minZoom > 1 ? this.minZoom : 1,
        x: 0,
        y: 0,
        username: this.userName,
        screen: {
          width: Math.min(this.viewportWidth, mainCanvasWidth),
          height: Math.min(this.viewportHeight, mainCanvasHeight),
        },
      })
    } else {
      this.updateScreenSize()
    }

    this.$nextTick(() => {
      this.getJoinedUsers()

      window.addEventListener('resize', this.updateViewportSizes)
      window.addEventListener('keydown', this.keyZoom)
      window.addEventListener('keyup', this.keyZoomUp)

      // Install event handlers for the pointer target
      const mainComponentEl = document.getElementById('main-component')
      const konvaEl = document.getElementById('konva')

      if (konvaEl) {
        konvaEl.addEventListener('touchmove', this.touchMove, false)
        konvaEl.addEventListener('touchend', this.touchUp, false)
      }

      if (mainComponentEl) {
        ;[
          'pointerdown',
          'pointerup',
          'pointerout',
          'pointerleave',
        ].forEach((evt) =>
          mainComponentEl.addEventListener(evt, this.pointerUpHandler, false)
        )

        mainComponentEl.addEventListener(
          'pointerdown',
          this.pointerDownHandler,
          false
        )
        mainComponentEl.addEventListener(
          'pointermove',
          this.pointerMoveHandler,
          false
        )
      }
    })
  },
  beforeDestroy() {
    window.removeEventListener('resize', this.updateViewportSizes)
    window.removeEventListener('keydown', this.keyZoom)
    window.removeEventListener('keyup', this.keyZoomUp)

    if (this.intervalId) {
      window.clearInterval(this.intervalId)
    }

    const mainComponentEl = document.getElementById('main-component')
    const konvaEl = document.getElementById('konva')

    if (konvaEl) {
      konvaEl.removeEventListener('touchmove', this.touchMove, false)
      konvaEl.removeEventListener('touchend', this.touchUp, false)
    }

    if (mainComponentEl) {
      ;[
        'pointerdown',
        'pointerup',
        'pointerout',
        'pointerleave',
      ].forEach((evt) =>
        mainComponentEl.removeEventListener(evt, this.pointerUpHandler, false)
      )

      mainComponentEl.removeEventListener(
        'pointerdown',
        this.pointerDownHandler,
        false
      )
      mainComponentEl.removeEventListener(
        'pointermove',
        this.pointerMoveHandler,
        false
      )
    }
  },
  methods: {
    pointerDownHandler(event) {
      this.userInteracted()

      // The pointerdown event signals the start of a touch interaction.
      // This event is cached to support 2-finger gestures
      this.evCache.push(event)

      if (this.evCache.length < 2) {
        const x = event.offsetX
        const y = event.offsetY

        this.mouseClickPosition.x = x + this.zoomAsset.asset.x
        this.mouseClickPosition.y = y + this.zoomAsset.asset.y
      }

      if (this.evCache.length === 2) {
        this.$store.commit(
          'classroom/setCursorNameBeforeChange',
          this.$store.state.classroom?.userParams?.cursor || 'cursor-pointer'
        )
        this.$store.commit(
          'classroom/setToolNameBeforeChange',
          this.$store.state.classroom?.userParams?.tool || 'pointer'
        )

        this.setTool('pointer', 'cursor-pointer')
      }
    },
    pointerMoveHandler(event) {
      if (!this.pointerMoveTimeout) {
        this.pointerMoveTimeout = setTimeout(() => {
          this.pointerMoveTimeout = null

          // This function implements a 2-pointer horizontal pinch/zoom gesture.
          // Find this event in the cache and update its record with this event
          for (let i = 0; i < this.evCache.length; i++) {
            if (event.pointerId === this.evCache[i].pointerId) {
              this.evCache[i] = event

              break
            }
          }

          // If two pointers are down, check for pinch gestures
          if (this.evCache.length === 2) {
            this.touchStatus = 'zooming'

            const oldZoomIndex = this.zoomIndex
            const step = isIOS() ? 0.005 : 0.025

            // Calculate the distance between the two pointers
            const curDiffX = Math.abs(
              this.evCache[0].clientX - this.evCache[1].clientX
            )
            const curDiffY = Math.abs(
              this.evCache[0].clientY - this.evCache[1].clientY
            )
            const curDiff = curDiffX > curDiffY ? curDiffX : curDiffY

            if (this.prevDiff > 0) {
              if (curDiff > this.prevDiff) {
                // The distance between the two pointers has increased
                const zoomIndex = Math.min(
                  Math.max(this.minZoom, oldZoomIndex + step),
                  this.maxZoom
                )

                this.zoomChange(oldZoomIndex, zoomIndex)
              }
              if (curDiff < this.prevDiff) {
                // The distance between the two pointers has decreased
                const zoomIndex = Math.min(
                  Math.max(this.minZoom, oldZoomIndex - step),
                  this.maxZoom
                )

                this.zoomChange(oldZoomIndex, zoomIndex)
              }
            }

            // Cache the distance for the next move event
            this.prevDiff = curDiff
          }
        }, 100)
      }
    },
    pointerUpHandler(event) {
      if (this.evCache.length === 2) {
        this.zoomStop()
        this.setTool(
          this.$store.state.classroom.toolNameBeforeChange,
          this.$store.state.classroom.cursorNameBeforeChange
        )
      }

      // Remove this pointer from the cache and reset the target's
      this.removeEvent(event)

      // If the number of pointers down is less than two then reset diff tracker
      if (this.evCache.length < 2) {
        this.prevDiff = -1
      }

      if (this.evCache.length === 0) {
        this.touchStatus = null
      }
    },
    removeEvent(event) {
      // Remove this event from the target's cache
      for (let i = 0; i < this.evCache.length; i++) {
        if (this.evCache[i].pointerId === event.pointerId) {
          this.evCache.splice(i, 1)

          break
        }
      }
    },
    getEventDeltaX(event) {
      if (event.deltaMode === WheelEvent.DOM_DELTA_PIXEL) {
        return event.deltaX / this.zoomIndex
      }

      return (event.deltaX * 10) / this.zoomIndex
    },
    getEventDeltaY(event) {
      if (event.deltaMode === WheelEvent.DOM_DELTA_PIXEL) {
        return event.deltaY / this.zoomIndex
      }

      return (event.deltaY * 10) / this.zoomIndex
    },
    getEventDeltaZoom(event) {
      let deltaY = event.deltaY

      const isFloat = Number(deltaY) === deltaY && deltaY % 1 !== 0

      if (isFloat) {
        deltaY =
          event.deltaY >= 0
            ? Math.ceil(event.deltaY + 7)
            : Math.floor(event.deltaY - 7)
      }

      if (event.deltaMode === WheelEvent.DOM_DELTA_PIXEL) {
        return deltaY * -0.0025
      }

      return deltaY * -0.01
    },
    updateViewportSizes() {
      this.viewportWidth = window.innerWidth
      this.viewportHeight = window.innerHeight

      this.updateScreenSize()
    },
    updateScreenSize() {
      if (this.zoomAsset && !this.screenResizeTimeout) {
        this.screenResizeTimeout = setTimeout(() => {
          this.screenResizeTimeout = null

          const data = {
            id: this.zoomAsset.id,
            lessonId: this.zoomAsset.lessonId,
            asset: {
              username: this.userName,
              screen: {
                width: Math.min(this.viewportWidth, mainCanvasWidth),
                height: Math.min(this.viewportHeight, mainCanvasHeight),
              },
            },
          }

          // this.$socket.emit('asset-moved', data)

          this.$store.commit('classroom/moveAsset', data)

          this.$store.dispatch('classroom/moveAsset', data)
        }, 100)
      }
    },
    keyZoomUp(event) {
      this.$store.commit('classroom/SET_IS_CTRL_KEY_DOWN', false)

      delete this.keysPressed[event.keyCode]
    },
    keyZoom(event) {
      this.$store.commit('classroom/SET_IS_CTRL_KEY_DOWN', event.ctrlKey)

      this.keysPressed[event.keyCode] = true

      if (
        this.keysPressed[17] &&
        (this.keysPressed[187] ||
          this.keysPressed[189] ||
          this.keysPressed[107] ||
          this.keysPressed[109])
      ) {
        event.preventDefault()

        const oldZoomIndex = this.zoomIndex

        const zoomIndex = [107, 187].includes(event.keyCode)
          ? Math.min(Math.max(this.minZoom, oldZoomIndex + 0.1), this.maxZoom)
          : Math.min(Math.max(this.minZoom, oldZoomIndex - 0.1), this.maxZoom)

        const x =
          (window.innerWidth * (zoomIndex - oldZoomIndex)) /
          zoomIndex /
          oldZoomIndex /
          2
        const y =
          (window.innerHeight * (zoomIndex - oldZoomIndex)) /
          zoomIndex /
          oldZoomIndex /
          2
        const asset = {
          zoomIndex,
          x:
            mainCanvasWidth * zoomIndex > this.viewportWidth
              ? Math.min(
                  Math.max(this.minSizeX, this.zoomAsset.asset.x + x),
                  this.maxSizeXPoint - window.innerWidth / zoomIndex
                )
              : 0,
          y:
            mainCanvasHeight * zoomIndex > this.viewportHeight
              ? Math.min(
                  Math.max(this.minSizeY, this.zoomAsset.asset.y + y),
                  this.maxSizeYPoint - window.innerHeight / zoomIndex
                )
              : 0,
        }

        this.$store.commit('classroom/moveAsset', {
          id: this.zoomAsset.id,
          asset,
        })

        this.$store.dispatch('classroom/moveAsset', {
          id: this.zoomAsset.id,
          lessonId: this.zoomAsset.lessonId,
          asset,
        })
      }
    },
    zoomChange(oldZoomIndex, zoomIndex) {
      const x =
        (window.innerWidth * (zoomIndex - oldZoomIndex)) /
        zoomIndex /
        oldZoomIndex /
        2
      const y =
        (window.innerHeight * (zoomIndex - oldZoomIndex)) /
        zoomIndex /
        oldZoomIndex /
        2

      this.$store.commit('classroom/moveAsset', {
        id: this.zoomAsset.id,
        asset: {
          zoomIndex,
          x:
            mainCanvasWidth * zoomIndex > this.viewportWidth
              ? Math.min(
                  Math.max(this.minSizeX, this.zoomAsset.asset.x + x),
                  this.maxSizeXPoint - window.innerWidth / zoomIndex
                )
              : 0,
          y:
            mainCanvasHeight * zoomIndex > this.viewportHeight
              ? Math.min(
                  Math.max(this.minSizeY, this.zoomAsset.asset.y + y),
                  this.maxSizeYPoint - window.innerHeight / zoomIndex
                )
              : 0,
        },
      })

      this.updateOtherCursor()
    },
    zoomStop() {
      this.$store.dispatch('classroom/moveAsset', {
        id: this.zoomAsset.id,
        lessonId: this.zoomAsset.lessonId,
        asset: {
          zoomIndex: this.zoomIndex,
          x: this.zoomAsset.asset.x,
          y: this.zoomAsset.asset.y,
        },
      })
    },
    gestureStart() {
      this.startScale = this.zoomIndex
    },
    gestureChange(event) {
      const oldZoomIndex = this.zoomIndex

      let zoomIndex = this.startScale * event.scale
      zoomIndex = Math.min(Math.max(this.minZoom, zoomIndex), this.maxZoom)

      const x =
        (event.clientX * (zoomIndex - oldZoomIndex)) / zoomIndex / oldZoomIndex
      const y =
        (event.clientY * (zoomIndex - oldZoomIndex)) / zoomIndex / oldZoomIndex

      this.$store.commit('classroom/moveAsset', {
        id: this.zoomAsset.id,
        asset: {
          zoomIndex,
          x:
            mainCanvasWidth * zoomIndex > this.viewportWidth
              ? Math.min(
                  Math.max(this.minSizeX, this.zoomAsset.asset.x + x),
                  this.maxSizeXPoint - window.innerWidth / zoomIndex
                )
              : 0,
          y:
            mainCanvasHeight * zoomIndex > this.viewportHeight
              ? Math.min(
                  Math.max(this.minSizeY, this.zoomAsset.asset.y + y),
                  this.maxSizeYPoint - window.innerHeight / zoomIndex
                )
              : 0,
        },
      })
    },
    gestureEnd() {
      this.$store.dispatch('classroom/moveAsset', {
        id: this.zoomAsset.id,
        lessonId: this.zoomAsset.lessonId,
        asset: {
          zoomIndex: this.zoomIndex,
          x: this.zoomAsset.asset.x,
          y: this.zoomAsset.asset.y,
        },
      })
    },
    onwheel(event) {
      if (event.ctrlKey) {
        event.preventDefault()

        const oldZoomIndex = this.zoomIndex

        let zoomIndex = oldZoomIndex + this.getEventDeltaZoom(event)
        zoomIndex = Math.min(Math.max(this.minZoom, zoomIndex), this.maxZoom)

        const x =
          (event.clientX * (zoomIndex - oldZoomIndex)) /
          zoomIndex /
          oldZoomIndex
        const y =
          (event.clientY * (zoomIndex - oldZoomIndex)) /
          zoomIndex /
          oldZoomIndex

        this.$store.commit('classroom/moveAsset', {
          id: this.zoomAsset.id,
          asset: {
            zoomIndex,
            x:
              mainCanvasWidth * zoomIndex > this.viewportWidth
                ? Math.min(
                    Math.max(this.minSizeX, this.zoomAsset.asset.x + x),
                    this.maxSizeXPoint - window.innerWidth / zoomIndex
                  )
                : 0,
            y:
              mainCanvasHeight * zoomIndex > this.viewportHeight
                ? Math.min(
                    Math.max(this.minSizeY, this.zoomAsset.asset.y + y),
                    this.maxSizeYPoint - window.innerHeight / zoomIndex
                  )
                : 0,
          },
        })

        this.onwheelStop(event)
      } else if (this.isScrollMainComponent) {
        event.preventDefault()

        this.$store.commit('classroom/moveAsset', {
          id: this.zoomAsset.id,
          asset: {
            x: Math.min(
              Math.max(
                this.minSizeX,
                this.zoomAsset.asset.x + this.getEventDeltaX(event)
              ),
              this.maxSizeX
            ),
            y: Math.min(
              Math.max(
                this.minSizeY,
                this.zoomAsset.asset.y + this.getEventDeltaY(event)
              ),
              this.maxSizeY
            ),
          },
        })

        this.onwheelStop(event)
      }

      this.updateOtherCursor()
    },
    onwheelStop: debounce(function (event) {
      if (event.ctrlKey) {
        const oldZoomIndex = this.zoomIndex

        let zoomIndex = oldZoomIndex + this.getEventDeltaZoom(event)
        zoomIndex = Math.min(Math.max(this.minZoom, zoomIndex), this.maxZoom)

        const x =
          (event.clientX * (zoomIndex - oldZoomIndex)) /
          zoomIndex /
          oldZoomIndex
        const y =
          (event.clientY * (zoomIndex - oldZoomIndex)) /
          zoomIndex /
          oldZoomIndex

        this.$store.dispatch('classroom/moveAsset', {
          id: this.zoomAsset.id,
          lessonId: this.zoomAsset.lessonId,
          asset: {
            zoomIndex,
            x:
              mainCanvasWidth * zoomIndex > this.viewportWidth
                ? Math.min(
                    Math.max(this.minSizeX, this.zoomAsset.asset.x + x),
                    this.maxSizeXPoint - window.innerWidth / zoomIndex
                  )
                : 0,
            y:
              mainCanvasHeight * zoomIndex > this.viewportHeight
                ? Math.min(
                    Math.max(this.minSizeY, this.zoomAsset.asset.y + y),
                    this.maxSizeYPoint - window.innerHeight / zoomIndex
                  )
                : 0,
          },
        })
      } else {
        this.$store.dispatch('classroom/moveAsset', {
          id: this.zoomAsset.id,
          lessonId: this.zoomAsset.lessonId,
          asset: {
            // x: Math.min(Math.max(this.minSizeX, this.zoomAsset.asset.x + this.getEventDeltaX(event)), this.maxSizeX),
            y: Math.min(
              Math.max(
                this.minSizeY,
                this.zoomAsset.asset.y + this.getEventDeltaY(event)
              ),
              this.maxSizeY
            ),
          },
        })
      }
    }, 500),
    konvaMouseUpHandler() {
      const bodyEl = document.body

      bodyEl.className = bodyEl.className.replace('dragging', '')
    },
    touchMove(event) {
      event.preventDefault()

      if (
        this.isDevice &&
        this.touchStatus !== 'zooming' &&
        this.userParams.tool === TOOL_POINTER &&
        this.evCache.length === 1
      ) {
        const x = event.targetTouches[0].clientX
        const y = event.targetTouches[0].clientY

        this.$store.commit('classroom/moveAsset', {
          id: this.zoomAsset.id,
          asset: {
            x: Math.min(
              Math.max(this.minSizeX, this.mouseClickPosition.x - x),
              this.maxSizeX
            ),
            y: Math.min(
              Math.max(this.minSizeY, this.mouseClickPosition.y - y),
              this.maxSizeY
            ),
          },
        })

        this.updateOtherCursor()
      }
    },
    touchUp() {
      if (
        this.touchStatus !== 'zooming' &&
        this.userParams.tool === TOOL_POINTER
      ) {
        this.mouseClickPosition.x = 0
        this.mouseClickPosition.y = 0

        this.$store.dispatch('classroom/moveAsset', {
          id: this.zoomAsset.id,
          lessonId: this.zoomAsset.lessonId,
          asset: {
            zoomIndex: this.zoomIndex,
            x: this.zoomAsset.asset.x,
            y: this.zoomAsset.asset.y,
          },
        })
      }
    },
    konvaMouseDownHandler(e) {
      if (this.userParams.tool === TOOL_POINTER) {
        this.mouseClickPosition.x = e.x + this.zoomAsset.asset.x
        this.mouseClickPosition.y = e.y + this.zoomAsset.asset.y

        addEvent(document.documentElement, 'mousemove', this.drag)

        document.body.classList.add('dragging')
      }
    },
    onUp() {
      if (this.userParams.tool === TOOL_POINTER) {
        this.mouseClickPosition.x = 0
        this.mouseClickPosition.y = 0

        removeEvent(document.documentElement, 'mousemove', this.drag)

        this.$store.dispatch('classroom/moveAsset', {
          id: this.zoomAsset.id,
          lessonId: this.zoomAsset.lessonId,
          asset: {
            zoomIndex: this.zoomIndex,
            x: this.zoomAsset.asset.x,
            y: this.zoomAsset.asset.y,
          },
        })
      }
    },
    drag(event) {
      if (!this.dragTimeout) {
        this.dragTimeout = setTimeout(() => {
          this.dragTimeout = null

          this.$store.commit('classroom/moveAsset', {
            id: this.zoomAsset.id,
            asset: {
              x: Math.min(
                Math.max(this.minSizeX, this.mouseClickPosition.x - event.x),
                this.maxSizeX
              ),
              y: Math.min(
                Math.max(this.minSizeY, this.mouseClickPosition.y - event.y),
                this.maxSizeY
              ),
            },
          })

          this.updateOtherCursor()
        }, 100)
      }
    },
    mouseMoved(event) {
      if (!this.mouseMoveTimeout) {
        this.mouseMoveTimeout = setTimeout(() => {
          this.mouseMoveTimeout = null

          this.$socket.emit('cursor-moved', {
            username: this.userName,
            coords: {
              x: event.clientX / this.zoomIndex + this.zoomAsset.asset.x,
              y: event.clientY / this.zoomIndex + this.zoomAsset.asset.y,
            },
            lessonId: this.lessonId,
          })
        }, 20)
      }
    },
    toggleBodyClass(addRemoveClass, className) {
      const el = document.body

      if (addRemoveClass === 'addClass') {
        el.classList.add(`${this.role}-${className}`)
      } else {
        el.classList.remove(`${this.role}-${className}`)
      }
    },
    updateOtherCursor() {
      this.$store.commit('classroom/updateOtherCursor', {
        coords: {
          x: this.otherCursorPosition.x,
          y: this.otherCursorPosition.y,
        },
      })
    },
    getJoinedUsers() {
      this.$socket.emit('get-users-joined-classroom')
    },
    closeVideoInputDialog() {
      this.$store.commit('classroom/closeVideoInput')
    },
    userInteracted() {
      if (!this.isUserInteracted) {
        this.$store.commit('classroom/USER_INTERACTED')
      }
    },
  },
  sockets: {
    connect() {
      this.getJoinedUsers()
    },
    disconnect() {
      this.getJoinedUsers()
    },
    async reconnect() {
      await this.$store.dispatch('loadingAllow', false)
      await this.$store
        .dispatch('classroom/getAssets', this.lessonId)
        .finally(() => this.$store.dispatch('loadingAllow', true))

      this.getJoinedUsers()
    },
    'cursor-moved'(data) {
      // console.log('cursor-moved', asset)
      if (data.coords?.x && data.coords?.y) {
        this.otherCursorPosition.x = data.coords.x
        this.otherCursorPosition.y = data.coords.y
      }

      this.$store.commit('classroom/updateOtherCursor', data)
    },
    'asset-added'(asset) {
      // console.log('asset-added', asset)
      this.$store.commit('classroom/addAssets', [asset])
    },
    'asset-deleted'(asset) {
      // console.log('asset-deleted', asset)
      this.$store.commit('classroom/deleteAsset', asset)
    },
    'asset-moved'(asset) {
      // console.log('asset-moved', asset)
      this.$store.commit('classroom/moveAsset', asset)
    },
    'users-joined-classroom'(asset) {
      // console.log('classroom-joined-users', asset)
      this.$store.commit('classroom/setUsersJoinedClassroom', asset.joinedUsers)
    },
    'user-left-classroom'() {
      // console.log('user-left-classroom')
      this.getJoinedUsers()
    },
  },
}
</script>

<style lang="scss">
@import './assets/styles/classroom.scss';
</style>

<style scoped>
.zoom-percent,
.classroom-timer {
  position: fixed;
  color: #80b723;
  line-height: 0.8;
  z-index: 99999;
}

.classroom-timer {
  font-size: 16px;
}

.zoom-percent {
  font-weight: 700;
  font-size: 18px;
  transition: opacity 0.5s;
  opacity: 0;
  top: 15px;
  right: 15px;
}

.zoom-percent--show {
  opacity: 1;
}

@media (max-width: 600px) {
  .zoom-percent {
    top: 5px;
  }

  .zoom-percent {
    right: 5px;
    font-size: 16px;
  }

  .classroom-timer {
    font-size: 14px;
  }
}
</style>
