<template>
  <v-app-bar
    :class="[
      'header',
      { 'header--dark': isDarkMode, 'header--light': !isDarkMode },
    ]"
    height="55"
    :absolute="isDarkMode"
    :color="isDarkMode ? 'darkLight' : '#f8faff'"
    :elevation="isDarkMode ? 0 : 3"
    app
  >
    <v-container fluid class="pa-0">
      <v-row no-gutters>
        <v-col class="col-12">
          <div class="header-wrapper d-flex align-center mx-auto">
            <div v-if="!isUserLogged" class="desktop-only-header">
              <div class="header-menu d-flex">
                <nav class="main-menu">
                  <ul class="main-menu-list d-flex">
                    <div class="header-logo">
                      <li class="main-menu-item d-flex align-center">
                        <nuxt-link to="/">
                          <img
                            :src="
                              require(`~/assets/images/${
                                isDarkMode ? 'logo-w.svg' : 'logo-lightMode.svg'
                              }`)
                            "
                            :alt="$t('langu')"
                          />
                        </nuxt-link>
                      </li>
                    </div>

                    <li
                      :id="[
                        isDarkMode
                          ? 'find-teacher-search-icon-svg-dark'
                          : 'find-teacher-search-icon-svg',
                      ]"
                      class="main-menu-item d-flex align-center"
                    >
                      <nuxt-link
                        class="main-menu-link"
                        :to="
                          localePath({
                            name: 'teacher-listing',
                          })
                        "
                      >
                        <div class="item-icon">
                          <SearchIcon />
                        </div>
                        {{ $t('find_teacher') }}
                      </nuxt-link>
                    </li>

                    <li class="main-menu-item d-flex align-center">
                      <nuxt-link
                        class="main-menu-link"
                        :to="localePath({ path: '/business' })"
                      >
                        {{ $t('for_companies') }}
                      </nuxt-link>
                    </li>

                    <li class="main-menu-item d-flex align-center">
                      <nuxt-link
                        class="main-menu-link"
                        :to="localePath({ path: '/education' })"
                      >
                        {{ $t('for_kids') }}
                      </nuxt-link>
                    </li>

                    <li class="main-menu-item d-flex align-center">
                      <nuxt-link
                        class="main-menu-link"
                        :to="localePath({ path: '/faq' })"
                      >
                        {{ $t('faq') }}
                      </nuxt-link>
                    </li>

                    <li class="main-menu-item d-flex align-center">
                      <a class="main-menu-link" @click="goToBlog">
                        {{ $t('blog') }}
                      </a>
                    </li>

                    <li class="main-menu-item d-flex align-center">
                      <nuxt-link
                        :to="localePath({ path: '/about-us' })"
                        class="d-md-flex align-center main-menu-link"
                      >
                        {{ $t('about_us') }}
                      </nuxt-link>
                    </li>
                  </ul>

                  <v-spacer></v-spacer>

                  <div
                    id="languageMenu"
                    class="language dropdown-menu d-none d-md-flex"
                    @mouseenter.stop="showLanguageMenu = true"
                    @mouseleave.stop="showLanguageMenu = false"
                  >
                    <v-btn
                      icon
                      color="transparent"
                      width="28"
                      height="21"
                      @click="showLanguageMenu = !showLanguageMenu"
                    >
                      <component :is="flagComponents[locale]"></component>
                    </v-btn>
                  </div>

                  <ul class="main-menu-list d-flex">
                    <li class="main-menu-item d-flex align-center">
                      <div
                        class="d-md-flex align-center main-menu-link"
                        @click.stop="showLoginSidebarClickHandler"
                      >
                        {{ $t('log_in') }}
                      </div>
                    </li>

                    <li class="main-menu-item d-flex align-center">
                      <div
                        id="signup-button"
                        class="d-md-flex align-center main-menu-link signup-button-header"
                        @click.stop="showLoginSidebarClickHandler"
                      >
                        {{ $t('sign_up_header') }}
                      </div>
                    </li>

                    <li
                      v-if="!isTeacherListingPage"
                      class="main-menu-item d-flex align-center"
                    >
                      <nuxt-link
                        id="book-a-trail-button-header"
                        :to="localePath({ path: '/teacher-listing' })"
                        class="d-md-flex align-center main-menu-link book-a-trail-button-header"
                      >
                        {{ $t('book_a_trail') }}
                      </nuxt-link>
                    </li>
                  </ul>
                </nav>
              </div>
            </div>

            <div v-if="isUserLogged && isStudent" class="desktop-only-header">
              <div class="header-menu d-flex">
                <nav class="main-menu main-menu-is-teacher">
                  <ul class="main-menu-list d-flex ul-is-teacher">
                    <div class="header-logo">
                      <li class="main-menu-item d-flex align-center">
                        <nuxt-link to="/">
                          <img
                            :src="
                              require(`~/assets/images/${
                                isDarkMode ? 'logo-w.svg' : 'Heylangu_Logo.webp'
                              }`)
                            "
                            :alt="$t('langu')"
                          />
                        </nuxt-link>
                      </li>
                    </div>

                    <div class="nav-bar-teacher-items">
                      <li class="main-menu-item d-flex align-center">
                        <nuxt-link
                          class="main-menu-link"
                          :to="
                            localePath({
                              name: 'teacher-listing',
                            })
                          "
                        >
                          <div class="item-icon find-teacher-icon">
                            <SearchIcon />
                          </div>
                          {{ $t('find_teacher') }}
                        </nuxt-link>
                      </li>

                      <li class="main-menu-item d-flex align-center">
                        <nuxt-link
                          class="main-menu-link"
                          :to="localePath({ path: '/user/lessons' })"
                        >
                          {{ $t('my_lessons') }}
                        </nuxt-link>
                      </li>

                      <li class="main-menu-item d-flex align-center">
                        <nuxt-link
                          :to="localePath('/user/messages')"
                          class="main-menu-link"
                        >
                          {{ $t('messages') }}
                          <span
                            v-if="newMessages"
                            class="l-badge main-menu-badge"
                          >
                            {{ newMessages }}
                          </span>
                        </nuxt-link>
                      </li>

                      <li class="main-menu-item d-flex align-center">
                        <nuxt-link
                          class="main-menu-link"
                          :to="localePath({ path: '/business' })"
                        >
                          {{ $t('for_companies') }}
                        </nuxt-link>
                      </li>

                      <li class="main-menu-item d-flex align-center">
                        <a class="main-menu-link" @click="goToBlog">
                          {{ $t('blog') }}
                        </a>
                      </li>

                      <div
                        id="mainMenu"
                        :class="[
                          'additional-menu dropdown-menu d-flex align-center',
                          { 'additional-menu--logged': isUserLogged },
                        ]"
                        @mouseenter.stop="showMainMenu = true"
                        @mouseleave.stop="showMainMenu = false"
                      >
                        <div
                          class="d-flex align-center"
                          @click="showMainMenu = !showMainMenu"
                        >
                          <span
                            class="d-none d-md-flex align-md-center user-link user-link--login user-link--avatar"
                          >
                            <v-avatar min-width="31" width="31" height="31">
                              <v-img :src="avatar"></v-img>
                            </v-avatar>

                            <span class="d-none d-md-inline-block">
                              {{ user.firstName }}
                            </span>

                            <span class="caret"></span>
                          </span>

                          <v-icon class="d-xl-none" color="dark" size="20">
                            {{ mdiMenu }}
                          </v-icon>
                        </div>
                      </div>
                    </div>
                  </ul>
                </nav>
              </div>
            </div>

            <div v-if="isUserLogged && isTeacher" class="desktop-only-header">
              <div class="header-menu d-flex">
                <nav class="main-menu main-menu-is-teacher">
                  <ul class="main-menu-list d-flex ul-is-teacher">
                    <div class="header-logo">
                      <li class="main-menu-item d-flex align-center">
                        <nuxt-link to="/">
                          <img
                            :src="
                              require(`~/assets/images/${
                                isDarkMode ? 'logo-w.svg' : 'Heylangu_Logo.webp'
                              }`)
                            "
                            :alt="$t('langu')"
                          />
                        </nuxt-link>
                      </li>
                    </div>

                    <div class="nav-bar-teacher-items">
                      <li class="main-menu-item d-flex align-center">
                        <nuxt-link
                          class="main-menu-link"
                          :to="localePath({ path: '/user/lessons' })"
                        >
                          {{ $t('my_lessons') }}
                        </nuxt-link>
                      </li>

                      <li class="main-menu-item d-flex align-center">
                        <nuxt-link
                          :to="localePath('/user/messages')"
                          class="main-menu-link"
                        >
                          {{ $t('messages') }}
                          <span
                            v-if="newMessages"
                            class="l-badge main-menu-badge"
                          >
                            {{ newMessages }}
                          </span>
                        </nuxt-link>
                      </li>

                      <li
                        v-if="isTeacher"
                        class="main-menu-item d-flex align-center"
                      >
                        <nuxt-link
                          class="main-menu-link"
                          :to="localePath('/user/payments')"
                        >
                          {{ $t('payments') }}
                        </nuxt-link>
                      </li>

                      <li class="main-menu-item d-flex align-center">
                        <nuxt-link
                          :to="localePath('/user/settings')"
                          class="main-menu-link"
                        >
                          {{ $t('my_settings') }}
                        </nuxt-link>
                      </li>

                      <li class="main-menu-item d-flex align-center">
                        <a class="main-menu-link" href="/user/availability">
                          {{ $t('availability') }}
                        </a>
                      </li>

                      <div
                        id="mainMenu"
                        :class="[
                          'additional-menu dropdown-menu d-flex align-center',
                          { 'additional-menu--logged': isUserLogged },
                        ]"
                        @mouseenter.stop="showMainMenu = true"
                        @mouseleave.stop="showMainMenu = false"
                      >
                        <div
                          class="d-flex align-center"
                          @click="showMainMenu = !showMainMenu"
                        >
                          <span
                            class="d-none d-md-flex align-md-center user-link user-link--login user-link--avatar"
                          >
                            <v-avatar min-width="31" width="31" height="31">
                              <v-img :src="avatar"></v-img>
                            </v-avatar>

                            <span class="d-none d-md-inline-block">
                              {{ user.firstName }}
                            </span>

                            <span class="caret"></span>
                          </span>

                          <v-icon class="d-xl-none" color="dark" size="20">
                            {{ mdiMenu }}
                          </v-icon>
                        </div>
                      </div>
                    </div>
                  </ul>
                </nav>
              </div>
            </div>

            <div v-if="isUserLogged && isTeacher" class="mobile-only-header">
              <div class="header-menu d-flex">
                <nav class="main-menu main-menu-is-teacher">
                  <ul class="main-menu-list ul-is-teacher">
                    <div class="header-logo">
                      <li class="main-menu-item d-flex align-center">
                        <nuxt-link to="/">
                          <img
                            :src="
                              require(`~/assets/images/${
                                isDarkMode ? 'logo-w.svg' : 'Heylangu_Logo.webp'
                              }`)
                            "
                            :alt="$t('langu')"
                          />
                        </nuxt-link>
                      </li>
                    </div>

                    <div class="nav-bar-teacher-items nav-bar-ham-menu-wrapper">
                      <v-icon
                        class="d-xl-none"
                        size="24"
                        @click="showMobileNav = true"
                      >
                        {{ mdiMenu }}
                      </v-icon>
                    </div>
                  </ul>
                </nav>
              </div>
            </div>

            <div v-if="isUserLogged && isStudent" class="mobile-only-header">
              <div class="header-menu d-flex">
                <nav class="main-menu main-menu-is-teacher">
                  <ul class="main-menu-list ul-is-teacher">
                    <div class="header-logo">
                      <li class="main-menu-item d-flex align-center">
                        <nuxt-link to="/">
                          <img
                            :src="
                              require(`~/assets/images/${
                                isDarkMode ? 'logo-w.svg' : 'Heylangu_Logo.webp'
                              }`)
                            "
                            :alt="$t('langu')"
                          />
                        </nuxt-link>
                      </li>
                    </div>

                    <div class="nav-bar-teacher-items nav-bar-ham-menu-wrapper">
                      <v-icon
                        class="d-xl-none"
                        size="24"
                        @click="showMobileNav = true"
                      >
                        {{ mdiMenu }}
                      </v-icon>
                    </div>
                  </ul>
                </nav>
              </div>
            </div>

            <div v-if="!isUserLogged" class="mobile-only-header">
              <div class="header-menu d-flex">
                <nav class="main-menu main-menu-is-teacher">
                  <ul class="main-menu-list ul-is-teacher">
                    <div class="header-logo">
                      <li class="main-menu-item d-flex align-center">
                        <nuxt-link to="/">
                          <img
                            :src="
                              require(`~/assets/images/${
                                isDarkMode ? 'logo-w.svg' : 'Heylangu_Logo.webp'
                              }`)
                            "
                            :alt="$t('langu')"
                          />
                        </nuxt-link>
                      </li>
                    </div>

                    <div class="nav-bar-teacher-items nav-bar-ham-menu-wrapper">
                      <v-icon
                        class="d-xl-none"
                        size="24"
                        @click="showMobileNav = true"
                      >
                        {{ mdiMenu }}
                      </v-icon>
                    </div>
                  </ul>
                </nav>
              </div>
            </div>
          </div>
        </v-col>
      </v-row>
    </v-container>

    <!--    Main menu-->

    <v-menu
      v-model="showMainMenu"
      absolute
      :attach="$vuetify.breakpoint.xlAndDown ? '.header' : '#mainMenu'"
      :min-width="$vuetify.breakpoint.smAndDown ? '100%' : 160"
      :nudge-bottom="$vuetify.breakpoint.smAndDown ? 55 : 26"
      :nudge-left="
        $vuetify.breakpoint.smAndDown ? 0 : isUserLogged ? 'initial' : 117
      "
      transition="v-expand-transition"
      close-on-content-click
      :content-class="`main-dropdown-menu${
        isUserLogged ? ' main-dropdown-menu--logged' : ''
      }`"
    >
      <v-list>
        <template>
          <div v-if="isUserLogged && isStudent">
            <v-list-item>
              <div class="main-dropdown-menu-item">
                <a href="/user/library">{{ $t('library') }}</a>
              </div>
            </v-list-item>
            <v-list-item>
              <div class="main-dropdown-menu-item">
                <a href="/meet">{{ $t('video_call_test') }}</a>
              </div>
            </v-list-item>

            <v-list-item>
              <div class="main-dropdown-menu-item">
                <nuxt-link :to="localePath({ path: '/education' })">
                  {{ $t('for_kids') }}
                </nuxt-link>
              </div>
            </v-list-item>

            <v-list-item>
              <div class="main-dropdown-menu-item">
                <nuxt-link :to="localePath({ path: '/faq' })">
                  {{ $t('faq') }}
                </nuxt-link>
              </div>
            </v-list-item>

            <v-list-item>
              <div class="main-dropdown-menu-item">
                <nuxt-link
                  :to="localePath({ path: '/about-us' })"
                  class="d-md-flex align-center"
                >
                  {{ $t('about_us') }}
                </nuxt-link>
              </div>
            </v-list-item>

            <v-list-item>
              <div class="main-dropdown-menu-item">
                <nuxt-link :to="localePath('/user/settings')">
                  {{ $t('my_settings') }}
                </nuxt-link>
              </div>
            </v-list-item>

            <v-list-item>
              <div class="main-dropdown-menu-item">
                <a href="/user/payments/receipts"> {{ $t('receipts') }} </a>
              </div>
            </v-list-item>

            <v-list-item>
              <div class="main-dropdown-menu-item signup-button-header">
                <a href="#" @click.prevent="logout"> {{ $t('logout') }} </a>
              </div>
            </v-list-item>
          </div>

          <div v-if="isUserLogged && isTeacher">
            <v-list-item>
              <div class="main-dropdown-menu-item">
                <a href="/meet">{{ $t('video_call_test') }}</a>
              </div>
            </v-list-item>
            <v-list-item>
              <div class="main-dropdown-menu-item">
                <nuxt-link :to="localePath({ path: '/business' })">
                  {{ $t('for_companies') }}
                </nuxt-link>
              </div>
            </v-list-item>

            <v-list-item>
              <div class="main-dropdown-menu-item">
                <nuxt-link :to="localePath({ path: '/education' })">
                  {{ $t('for_kids') }}
                </nuxt-link>
              </div>
            </v-list-item>

            <v-list-item>
              <div class="main-dropdown-menu-item">
                <nuxt-link :to="localePath({ path: '/faq' })">
                  {{ $t('faq') }}
                </nuxt-link>
              </div>
            </v-list-item>

            <v-list-item>
              <div class="main-dropdown-menu-item">
                <a @click="goToBlog"> {{ $t('blog') }} </a>
              </div>
            </v-list-item>

            <v-list-item>
              <div class="main-dropdown-menu-item">
                <nuxt-link :to="localePath({ path: '/about-us' })">
                  {{ $t('about_us') }}
                </nuxt-link>
              </div>
            </v-list-item>

            <v-list-item>
              <div class="main-dropdown-menu-item">
                <a href="/user/library"> {{ $t('library') }} </a>
              </div>
            </v-list-item>

            <v-list-item>
              <div class="main-dropdown-menu-item">
                <a href="/user/reviews">{{ $t('reviews') }}</a>
              </div>
            </v-list-item>

            <v-list-item>
              <div class="main-dropdown-menu-item signup-button-header">
                <a id="logout-button-in-modal" href="#" @click.prevent="logout">
                  {{ $t('logout') }}
                </a>
              </div>
            </v-list-item>
          </div>
        </template>
      </v-list>
    </v-menu>

    <div v-if="showMobileNav && isUserLogged && isTeacher">
      <ul
        id="mobile-nav-teacher"
        class="main-menu-list d-flex ul-is-teacher mobile-nav-teacher"
      >
        <div class="close-button-nav-bar">
          <v-icon class="d-xl-none" size="24" @click="showMobileNav = false">
            {{ mdiClose }}
          </v-icon>
        </div>

        <div
          id="mainMenu"
          :class="[
            'additional-menu dropdown-menu d-flex align-center',
            { 'additional-menu--logged': isUserLogged },
          ]"
          @click="isAvatarClicked = !isAvatarClicked"
        >
          <div class="d-flex align-center">
            <span
              class="d-md-flex align-md-center user-link user-link--login user-link--avatar ml-4 mb-3 mt-3"
            >
              <v-avatar min-width="31" width="31" height="31">
                <v-img :src="avatar"></v-img>
              </v-avatar>

              <span class="d-md-inline-block ml-1 first-name">
                {{ user.firstName }}
              </span>

              <v-icon id="avatar-down-arrow" color="greyDark">
                {{ mdiChevronDown }}
              </v-icon>
            </span>
          </div>
        </div>

        <div v-if="isAvatarClicked" class="on-avatar-click-menu">
          <v-list-item>
            <nuxt-link
              :to="localePath({ path: '/business' })"
              class="on-click-avatar-submenu mt-1"
            >
              {{ $t('for_companies') }}
            </nuxt-link>
          </v-list-item>

          <v-list-item>
            <div class="main-dropdown-menu-item">
              <nuxt-link
                :to="localePath({ path: '/education' })"
                class="on-click-avatar-submenu"
              >
                {{ $t('for_kids') }}
              </nuxt-link>
            </div>
          </v-list-item>

          <v-list-item>
            <div class="main-dropdown-menu-item">
              <nuxt-link
                :to="localePath({ path: '/faq' })"
                class="on-click-avatar-submenu"
              >
                {{ $t('faq') }}
              </nuxt-link>
            </div>
          </v-list-item>

          <v-list-item>
            <div class="main-dropdown-menu-item">
              <a class="on-click-avatar-submenu" @click="goToBlog">
                {{ $t('blog') }}
              </a>
            </div>
          </v-list-item>

          <v-list-item>
            <div class="main-dropdown-menu-item">
              <nuxt-link
                :to="localePath({ path: '/about-us' })"
                class="on-click-avatar-submenu"
              >
                {{ $t('about_us') }}
              </nuxt-link>
            </div>
          </v-list-item>

          <v-list-item>
            <div class="main-dropdown-menu-item">
              <a href="/user/library" class="on-click-avatar-submenu">
                {{ $t('library') }}
              </a>
            </div>
          </v-list-item>

          <v-list-item>
            <div class="main-dropdown-menu-item mb-1">
              <a href="/user/reviews" class="on-click-avatar-submenu">
                {{ $t('reviews') }}
              </a>
            </div>
          </v-list-item>
        </div>

        <li
          :class="[
            'main-menu-item d-flex align-center',
            $route.path.includes('meet') ? 'hightlighted-nav-bar-item' : '',
          ]"
        >
          <a class="main-menu-link-mob-nav" href="/meet">
            {{ $t('video_call_test') }}
          </a>
        </li>

        <li
          :class="[
            'main-menu-item d-flex align-center',
            $route.path.includes('user/lessons')
              ? 'hightlighted-nav-bar-item'
              : '',
          ]"
        >
          <nuxt-link
            class="main-menu-link-mob-nav"
            :to="localePath({ path: '/user/lessons' })"
          >
            {{ $t('my_lessons') }}
          </nuxt-link>
        </li>

        <li
          :class="[
            'main-menu-item d-flex align-center',
            $route.path.includes('user/messages')
              ? 'hightlighted-nav-bar-item'
              : '',
          ]"
        >
          <nuxt-link
            :to="localePath('/user/messages')"
            class="main-menu-link-mob-nav"
          >
            {{ $t('messages') }}
            <span v-if="newMessages" class="l-badge main-menu-badge">
              {{ newMessages }}
            </span>
          </nuxt-link>
        </li>

        <li
          :class="[
            'main-menu-item d-flex align-center',
            $route.path.includes('user/payments')
              ? 'hightlighted-nav-bar-item'
              : '',
          ]"
        >
          <nuxt-link
            class="main-menu-link-mob-nav"
            :to="localePath('/user/payments')"
          >
            {{ $t('payments') }}
          </nuxt-link>
        </li>

        <li
          :class="[
            'main-menu-item d-flex align-center',
            $route.path.includes('user/settings')
              ? 'hightlighted-nav-bar-item'
              : '',
          ]"
        >
          <nuxt-link
            :to="localePath('/user/settings')"
            class="main-menu-link-mob-nav"
          >
            {{ $t('my_settings') }}
          </nuxt-link>
        </li>

        <li
          :class="[
            'main-menu-item d-flex align-center',
            $route.path.includes('user/availability')
              ? 'hightlighted-nav-bar-item'
              : '',
          ]"
        >
          <a class="main-menu-link-mob-nav" href="/user/availability">
            {{ $t('availability') }}
          </a>
        </li>

        <div
          class="main-dropdown-menu-item signup-button-header main-menu-link-mob-nav"
        >
          <a id="logout-button-in-modal" href="#" @click.prevent="logout">
            {{ $t('logout') }}
          </a>
        </div>
      </ul>
    </div>

    <div v-if="showMobileNav && isUserLogged && isStudent">
      <ul
        id="mobile-nav-teacher"
        class="main-menu-list d-flex ul-is-teacher mobile-nav-teacher"
      >
        <div class="close-button-nav-bar">
          <v-icon class="d-xl-none" size="24" @click="showMobileNav = false">
            {{ mdiClose }}
          </v-icon>
        </div>

        <div
          id="mainMenu"
          :class="[
            'additional-menu dropdown-menu d-flex align-center',
            { 'additional-menu--logged': isUserLogged },
          ]"
          @click="isAvatarClicked = !isAvatarClicked"
        >
          <div class="d-flex align-center">
            <span
              class="d-md-flex align-md-center user-link user-link--login user-link--avatar ml-4 mb-3 mt-3"
            >
              <v-avatar min-width="31" width="31" height="31">
                <v-img :src="avatar"></v-img>
              </v-avatar>

              <span class="d-md-inline-block ml-1 first-name">
                {{ user.firstName }}
              </span>

              <v-icon id="avatar-down-arrow" color="greyDark">
                {{ mdiChevronDown }}
              </v-icon>
            </span>
          </div>
        </div>

        <div v-if="isAvatarClicked" class="on-avatar-click-menu">
          <v-list-item>
            <div class="main-dropdown-menu-item">
              <a href="/user/library" class="on-click-avatar-submenu">
                {{ $t('library') }}
              </a>
            </div>
          </v-list-item>

          <v-list-item>
            <div class="main-dropdown-menu-item">
              <nuxt-link
                :to="localePath({ path: '/education' })"
                class="on-click-avatar-submenu"
              >
                {{ $t('for_kids') }}
              </nuxt-link>
            </div>
          </v-list-item>

          <v-list-item>
            <div class="main-dropdown-menu-item">
              <nuxt-link
                :to="localePath({ path: '/faq' })"
                class="on-click-avatar-submenu"
              >
                {{ $t('faq') }}
              </nuxt-link>
            </div>
          </v-list-item>

          <v-list-item>
            <div class="main-dropdown-menu-item">
              <nuxt-link
                :to="localePath({ path: '/about-us' })"
                class="d-md-flex align-center on-click-avatar-submenu"
              >
                {{ $t('about_us') }}
              </nuxt-link>
            </div>
          </v-list-item>

          <v-list-item>
            <div class="main-dropdown-menu-item">
              <nuxt-link
                :to="localePath('/user/settings')"
                class="on-click-avatar-submenu"
              >
                {{ $t('my_settings') }}
              </nuxt-link>
            </div>
          </v-list-item>

          <v-list-item>
            <div class="main-dropdown-menu-item">
              <a href="/user/payments/receipts" class="on-click-avatar-submenu">
                {{ $t('receipts') }}
              </a>
            </div>
          </v-list-item>
        </div>

        <li
          :id="[
            isDarkMode
              ? 'find-teacher-search-icon-svg-dark'
              : 'find-teacher-search-icon-svg',
          ]"
          :class="[
            'main-menu-item d-flex align-center',
            $route.path.includes('teacher-listing')
              ? 'hightlighted-teacher-listing'
              : '',
          ]"
        >
          <nuxt-link
            class="main-menu-link-mob-nav"
            :to="
              localePath({
                name: 'teacher-listing',
              })
            "
          >
            <div class="item-icon">
              <SearchIcon />
            </div>
            {{ $t('find_teacher') }}
          </nuxt-link>
        </li>

        <li
          :class="[
            'main-menu-item d-flex align-center',
            $route.path.includes('meet') ? 'hightlighted-nav-bar-item' : '',
          ]"
        >
          <a class="main-menu-link-mob-nav" href="/meet">
            {{ $t('video_call_test') }}
          </a>
        </li>

        <li
          :class="[
            'main-menu-item d-flex align-center',
            $route.path.includes('user/lessons')
              ? 'hightlighted-nav-bar-item'
              : '',
          ]"
        >
          <nuxt-link
            class="main-menu-link-mob-nav"
            :to="localePath({ path: '/user/lessons' })"
          >
            {{ $t('my_lessons') }}
          </nuxt-link>
        </li>

        <li
          :class="[
            'main-menu-item d-flex align-center',
            $route.path.includes('user/messages')
              ? 'hightlighted-nav-bar-item'
              : '',
          ]"
        >
          <nuxt-link
            :to="localePath('/user/messages')"
            class="main-menu-link-mob-nav"
          >
            {{ $t('messages') }}
            <span v-if="newMessages" class="l-badge main-menu-badge">
              {{ newMessages }}
            </span>
          </nuxt-link>
        </li>

        <li class="main-menu-item d-flex align-center">
          <nuxt-link
            :class="[
              'main-menu-link-mob-nav',
              $route.path.includes('business')
                ? 'hightlighted-nav-bar-item'
                : '',
            ]"
            :to="localePath({ path: '/business' })"
          >
            {{ $t('for_companies') }}
          </nuxt-link>
        </li>

        <li
          :class="[
            'main-menu-item d-flex align-center',
            $route.path.includes('blog') ? 'hightlighted-nav-bar-item' : '',
          ]"
        >
          <a class="main-menu-link-mob-nav" @click="goToBlog">
            {{ $t('blog') }}
          </a>
        </li>

        <div
          class="main-dropdown-menu-item signup-button-header main-menu-link-mob-nav"
        >
          <a id="logout-button-in-modal" href="#" @click.prevent="logout">
            {{ $t('logout') }}
          </a>
        </div>
      </ul>
    </div>

    <div v-if="showMobileNav && !isUserLogged">
      <ul
        id="mobile-nav-teacher"
        class="main-menu-list d-flex ul-is-teacher mobile-nav-teacher"
      >
        <div class="close-button-nav-bar">
          <v-icon class="d-xl-none" size="24" @click="showMobileNav = false">
            {{ mdiClose }}
          </v-icon>
        </div>

        <li
          :id="[
            isDarkMode
              ? 'find-teacher-search-icon-svg-dark'
              : 'find-teacher-search-icon-svg',
          ]"
          :class="[
            'main-menu-item d-flex align-center',
            $route.path.includes('teacher-listing')
              ? 'hightlighted-teacher-listing'
              : '',
          ]"
        >
          <nuxt-link
            class="main-menu-link-mob-nav"
            :to="
              localePath({
                name: 'teacher-listing',
              })
            "
          >
            <div class="item-icon">
              <SearchIcon />
            </div>
            {{ $t('find_teacher') }}
          </nuxt-link>
        </li>

        <li class="main-menu-item d-flex align-center">
          <nuxt-link
            :class="[
              'main-menu-link-mob-nav',
              $route.path.includes('business')
                ? 'hightlighted-nav-bar-item'
                : '',
            ]"
            :to="localePath({ path: '/business' })"
          >
            {{ $t('for_companies') }}
          </nuxt-link>
        </li>

        <li class="main-menu-item d-flex align-center">
          <nuxt-link
            :class="[
              'main-menu-link-mob-nav',
              $route.path.includes('education')
                ? 'hightlighted-nav-bar-item'
                : '',
            ]"
            :to="localePath({ path: '/education' })"
          >
            {{ $t('for_kids') }}
          </nuxt-link>
        </li>

        <li class="main-menu-item d-flex align-center">
          <nuxt-link
            :class="[
              'main-menu-link-mob-nav',
              $route.path.includes('faq') ? 'hightlighted-nav-bar-item' : '',
            ]"
            :to="localePath({ path: '/faq' })"
          >
            {{ $t('faq') }}
          </nuxt-link>
        </li>

        <li
          :class="[
            'main-menu-item d-flex align-center',
            $route.path.includes('blog') ? 'hightlighted-nav-bar-item' : '',
          ]"
        >
          <a class="main-menu-link-mob-nav" @click="goToBlog">
            {{ $t('blog') }}
          </a>
        </li>

        <li
          :class="[
            'main-menu-item d-flex align-center',
            $route.path.includes('about-us') ? 'hightlighted-nav-bar-item' : '',
          ]"
        >
          <nuxt-link
            :to="localePath({ path: '/about-us' })"
            class="d-md-flex align-center main-menu-link-mob-nav"
          >
            {{ $t('about_us') }}
          </nuxt-link>
        </li>

        <div
          id="languageMenu"
          class="language dropdown-menu d-md-flex main-menu-link-mob-nav"
        >
          <v-btn
            icon
            color="transparent"
            width="28"
            height="21"
            @click="showLanguageMenu = !showLanguageMenu"
          >
            <component :is="flagComponents[locale]"></component>
          </v-btn>
          Website language: {{ locale.toUpperCase() }}
        </div>

        <v-list v-if="showLanguageMenu">
          <v-list-item v-for="(l, idx) in dropdownLanguages" :key="idx">
            <div
              class="main-dropdown-menu-item main-dropdown-menu-item--flag mobile-nav-language-selector"
            >
              <a
                :href="switchLocalePath(l.code)"
                class="d-flex align-center text-capitalize main-menu-link-mob-nav"
              >
                <v-img
                  :src="require(`~/assets/images/flags/${l.code}.svg`)"
                  width="26"
                  height="19"
                ></v-img>

                <div>{{ $t(l.name) }}</div>
              </a>
            </div>
          </v-list-item>
        </v-list>

        <li class="main-menu-item d-flex align-center">
          <div
            class="d-md-flex align-center main-menu-link-mob-nav login-link"
            @click.stop="showLoginSidebarClickHandler"
          >
            {{ $t('log_in') }}
          </div>
        </li>

        <li class="main-menu-item d-flex align-center">
          <nuxt-link
            id="signup-button"
            :to="localePath({ path: '/user/register' })"
            class="d-md-flex align-center main-menu-link-mob-nav signup-button-header"
          >
            {{ $t('sign_up_header') }}
          </nuxt-link>
        </li>

        <li
          v-if="!isTeacherListingPage"
          class="main-menu-item d-flex align-center"
        >
          <nuxt-link
            id="book-a-trail-button-header"
            :to="localePath({ path: '/user/register' })"
            class="d-md-flex align-center main-menu-link-mob-nav book-a-trail-button-header"
          >
            {{ $t('book_a_trail') }}
          </nuxt-link>
        </li>
      </ul>
    </div>

    <!--    Language menu-->

    <v-menu
      v-model="showLanguageMenu"
      attach="#languageMenu"
      nudge-left="108"
      nudge-bottom="26"
      close-on-content-click
      min-width="160"
      transition="v-expand-transition"
      content-class="main-dropdown-menu"
    >
      <v-list>
        <v-list-item v-for="(l, idx) in dropdownLanguages" :key="idx">
          <div class="main-dropdown-menu-item main-dropdown-menu-item--flag">
            <a
              :href="switchLocalePath(l.code)"
              class="d-flex align-center text-capitalize"
            >
              <v-img
                :src="require(`~/assets/images/flags/${l.code}.svg`)"
                width="26"
                height="19"
              ></v-img>
              {{ $t(l.name) }}
            </a>
          </div>
        </v-list-item>
      </v-list>
    </v-menu>
  </v-app-bar>
</template>

<script>
import { mdiMenu, mdiChevronDown, mdiClose } from '@mdi/js'
import EnFlagIcon from '~/components/images/EnFlagIcon.vue'
import PlFlagIcon from '~/components/images/PlFlagIcon.vue'
import EsFlagIcon from '~/components/images/EsFlagIcon.vue'
import SearchIcon from '~/components/images/SearchIcon.vue'

export default {
  name: 'Header',
  components: {
    SearchIcon,
  },
  data() {
    return {
      mdiMenu,
      mdiClose,
      mdiChevronDown,
      showMainMenu: false,
      showLanguageMenu: false,
      drawer: false,
      group: null,
      flagComponents: {
        en: EnFlagIcon,
        pl: PlFlagIcon,
        es: EsFlagIcon,
      },
      showMobileNav: false,
      isAvatarClicked: false,
    }
  },
  computed: {
    locale() {
      return this.$i18n.locale
    },
    locales() {
      return this.$store.state.locales
    },
    dropdownLanguages() {
      return this.locales.filter((item) => item.code !== this.locale)
    },
    user() {
      return this.$store.state.user.item
    },
    isStudent() {
      return this.$store.getters['user/isStudent']
    },
    isTeacher() {
      return this.$store.getters['user/isTeacher']
    },
    isUserLogged() {
      return this.$store.getters['user/isUserLogged']
    },
    newMessages() {
      return this.$store.getters['user/newMessages']
    },
    avatar() {
      return this.user?.userPicture?.length
        ? this.user.userPicture
        : require('~/assets/images/avatar.png')
    },
    isDarkMode() {
      const routeRegex = this.$route.matched[0]?.regex
      return routeRegex?.test('/') || routeRegex?.test('/business')
    },
    isTeacherListingPage() {
      const routeRegex = this.$route.matched[0]?.regex
      return (
        routeRegex?.test('/teacher-listing') ||
        routeRegex?.test('/teacher-listing/123') ||
        routeRegex?.test('/teacher-listing/123/abc')
      )
    },
  },
  watch: {
    $route() {
      this.showMobileNav = false
    },
  },
  methods: {
    showLoginSidebarClickHandler() {
      this.$store.commit('SET_IS_LOGIN_SIDEBAR', true)
    },
    logout() {
      this.$store.dispatch('auth/logout').then(() => {
        this.$socket.disconnect()

        window.location.href = '/'
      })
    },
    goToBlog() {
      window.location.href = '/blog'
    },
  },
}
</script>

<style lang="scss">
@import '~vuetify/src/styles/settings/_variables';

.v-sheet.v-app-bar.v-toolbar:not(.v-sheet--outlined) {
  box-shadow: none !important;
  height: 100px !important;
  padding-top: 10px;
}

.v-toolbar__content {
  height: 85px !important;
  margin-left: 90px;
  margin-right: 90px;
}

.header {
  &-wrapper {
    max-width: 1920px;
  }

  &-logo img {
    display: block;
    width: auto;
    height: 51px;
  }

  &-menu {
    .main-menu,
    .user {
      &-item {
        min-height: 25px;

        &--icon-end {
          a {
            .v-image {
              margin-right: 0;
              margin-left: 5px;
            }
          }
        }
      }

      &-link {
        font-size: 18px;
        text-decoration: none;
        color: var(--v-dark-base) !important;
        fill: black;
        letter-spacing: 0.1px;
        line-height: 19px;
        text-align: center;
        cursor: pointer;

        @media #{map-get($display-breakpoints, 'md-only')} {
          font-size: 14px;
        }

        &:hover {
          color: var(--v-orange-base) !important;
          fill: #fbb03b;
        }

        &--avatar {
          position: relative;
          padding-left: 36px;

          .v-avatar {
            position: absolute;
            left: 0;
            top: 50%;
            transform: translateY(-50%);
          }
        }

        .v-image {
          @media #{map-get($display-breakpoints, 'md-and-up')} {
            margin-right: 3px;
          }
        }
      }
    }

    .main-menu {
      &-item {
        padding: 0 15px;
        @media #{map-get($display-breakpoints, 'md-only')} {
          padding: 0 14px;
        }

        &:last-child {
          margin-right: 0;
          border-right: none;
        }
      }

      &-link {
        display: flex;
        align-items: center;
      }

      &-badge {
        margin-left: 3px;
      }
    }

    .additional-menu:not(.additional-menu--logged) {
      width: 55px;

      @media #{map-get($display-breakpoints, 'md-only')} {
        width: 44px;
      }
    }

    .language {
      width: 70px;

      @media #{map-get($display-breakpoints, 'md-only')} {
        width: 44px;
      }
    }

    .additional-menu,
    .user,
    .language {
      position: relative;
      padding: 0 20px;

      @media #{map-get($display-breakpoints, 'md-only')} {
        padding: 0 14px;
      }

      @media #{map-get($display-breakpoints, 'sm-and-down')} {
        padding: 0 15px;
        border-left: none;
      }
    }

    .user {
      @media #{map-get($display-breakpoints, 'sm-and-down')} {
        padding: 0 5px;

        &-link {
          padding: 0 10px;
        }
      }
    }

    .language {
      min-height: 35px;
      align-items: center;
      @media #{map-get($display-breakpoints, 'md-only')} {
        padding-right: 0;
      }

      button svg {
        box-shadow: 0 0 1px 0 #aaa;
      }
    }
  }

  ul {
    padding-left: 0 !important;
    list-style-type: none;
  }

  &--dark {
    .header-menu .main-menu-link,
    .header-menu .user-link {
      color: #fff !important;
      fill: white !important;

      &:hover {
        color: #fbb03b !important;
        fill: #fbb03b !important;
      }
    }

    .header-menu .main-menu-item,
    .header-menu .additional-menu,
    .header-menu .user,
    .header-menu .language {
      border-color: #fff;
    }
  }
}

.main-dropdown {
  &-menu {
    border-radius: 25px !important;
    top: 27px !important;
    left: -40px !important;
    box-shadow: 0 1px 4px rgb(0 0 0 / 30%) !important;

    @media #{map-get($display-breakpoints, 'sm-and-down')} {
      border-bottom: 1px solid rgba(0, 0, 0, 0.2);
      border-radius: 0 !important;
      box-shadow: none !important;
    }

    .v-list {
      padding: 5px 0 !important;

      @media #{map-get($display-breakpoints, 'sm-and-down')} {
        padding: 10px 0 !important;
      }

      &-item {
        min-height: 29px !important;
        padding: 3px 20px !important;
        line-height: 1.1;

        @media #{map-get($display-breakpoints, 'sm-and-down')} {
          margin-top: 9px;
          margin-bottom: 9px;
          padding: 0 12px !important;
        }

        .main-dropdown-menu-item {
          text-align: right;

          &--flag {
            a {
              .v-image {
                box-shadow: 0 0 1px 0 #aaa;
              }
            }
          }

          &--icon-end {
            a {
              .v-image {
                margin-right: 0;
                margin-left: 5px;
              }
            }
          }

          .l-badge {
            margin-left: 5px;
          }

          &:not(.main-dropdown-menu-item--flag) {
            width: 100%;
          }
        }

        a {
          display: flex;
          align-items: center;
          color: var(--v-dark-base) !important;
          font-size: 13px;
          text-decoration: none;

          @media #{map-get($display-breakpoints, 'md-and-up')} {
            font-weight: 700;
          }

          @media #{map-get($display-breakpoints, 'sm-and-down')} {
            font-size: 14px;
            text-transform: uppercase;
          }

          &:hover {
            color: var(--v-orange-base) !important;
          }

          .v-image {
            margin-right: 5px;
          }
        }
      }
    }

    &--logged {
      @media #{map-get($display-breakpoints, 'md-and-up')} {
        right: 0;
      }

      .v-list-item {
        min-width: 160px;

        a {
          @media #{map-get($display-breakpoints, 'md-and-up')} {
            justify-content: flex-end;
          }
        }
      }
    }
  }
}

.search-icon {
  width: 20px;
  margin-top: 3px;
  margin-right: 10px;
}

.header-wrapper {
  margin-left: 90px !important;
}

.header-logo img {
  height: 70px !important;
}

.header-logo {
  padding-top: 15px;
}

.header-wrapper > div {
  width: 100%;
}

.main-menu {
  display: flex !important;
  width: 100%;
  align-items: center;
}

.signup-button-header {
  border-radius: 50px;
  border: 1px solid #fbb03b;
  padding: 10px 25px;
  width: fit-content !important;
}

#book-a-trail-button-header {
  border-radius: 50px;
  background-color: #fbb03b;
  padding: 10px 40px;
  font-weight: bold !important;
  color: white !important;
}

.main-menu-is-teacher {
  display: flex;
  justify-content: space-between;
  width: 100% !important;
}

.nav-bar-teacher-items {
  display: flex !important;
}

.ul-is-teacher {
  width: 100%;
  display: flex !important;
  justify-content: space-between !important;
}

.v-menu__content {
  min-width: 160px;
  top: 80px;
  left: -17px;
  transform-origin: left top;
  z-index: 103;
  border-radius: 20px;
}

.v-application .white {
  background-color: #f8faff !important;
}

.signup-button-header {
  position: relative;
  display: inline-block;
  padding: 10px 20px;
  font-size: 16px;
  color: #fff;
  background-color: transparent;
  border: 1px solid #fbb03b;
  overflow: hidden;
  transition: color 0.4s;
  z-index: 1;
}

.signup-button-header::before {
  content: '';
  position: absolute;
  bottom: 0%;
  left: 50%;
  width: 150%;
  height: 300%;
  background-color: #fbb03b;
  border-radius: 50%;
  transition: transform 0.4s ease-out;
  transform: translate(-50%, 50%) scale(0);
  z-index: -1;
}

.signup-button-header:hover {
  color: #fff;
}

.signup-button-header:hover::before {
  transform: translate(-50%, 50%) scale(1);
}

.header--dark .signup-button-header:hover {
  color: white !important;
}

.book-a-trail-button-header {
  position: relative;
  display: inline-block;
  padding: 10px 20px;
  font-size: 16px;
  color: #fff;
  background-color: transparent;
  border: 1px solid #fbb03b;
  overflow: hidden;
  transition: color 0.4s;
  z-index: 1;
}

.book-a-trail-button-header::before {
  content: '';
  position: absolute;
  bottom: 0%;
  left: 50%;
  width: 150%;
  height: 300%;
  background-color: #fff;
  border-radius: 50%;
  transition: transform 0.4s ease-out;
  transform: translate(-50%, 50%) scale(0);
  z-index: -1;
  opacity: 0.25;
}

.book-a-trail-button-header:hover {
  color: #fff;
}

.book-a-trail-button-header:hover::before {
  transform: translate(-50%, 50%) scale(1);
}

.header--dark .book-a-trail-button-header:hover {
  color: white !important;
}

.header--white .book-a-trail-button-header:hover {
  color: white !important;
}

#signup-button:hover {
  color: white !important;
}

.main-menu-item:hover .find-teacher-icon:hover {
  fill: #fbb03b;
}

.login-link:hover {
  cursor: pointer;
}

#find-teacher-search-icon-svg svg {
  fill: black !important;
}

#find-teacher-search-icon-svg:hover svg {
  fill: #fbb03b !important;
}

#find-teacher-search-icon-svg-dark svg {
  fill: white !important;
}

#find-teacher-search-icon-svg-dark:hover svg {
  fill: #fbb03b !important;
}

.v-toolbar__content,
.menuable__content__active {
  border-radius: 25px;
}

#logout-button-in-modal:hover {
  color: white !important;
}

.desktop-only-header {
  display: block;
}

.mobile-only-header {
  display: none;
}

#mobile-nav-teacher {
  display: none !important;
}

@media screen and (max-width: 1200px) {
  .ul-is-teacher {
    width: 47vw;
  }
  .mobile-only-header {
    display: block;
    margin-left: -100px;
  }

  #find-teacher-search-icon-svg-dark:hover svg {
    fill: black !important;
  }

  .desktop-only-header {
    display: none;
  }

  #mobile-nav-teacher {
    background: white;
    padding-left: 10px;
    border-radius: 0px 0px 0px 25px;
    min-height: 650px;
    right: 0px;
    margin-top: 100px;
    display: block !important;
    width: 50vw;
    position: fixed;
    top: -100px;
    width: 50vw;
    place-items: start;
    position: fixed;
    top: -100px;
    width: 70vw;
    font-size: 18px !important;
  }

  .teacher-listing-page #mobile-nav-teacher {
    background: white;
    padding-left: 10px;
    border-radius: 0px 0px 0px 25px;
    min-height: 650px;
    right: 20px;
    margin-top: 100px;
    display: block !important;
    width: 50vw;
    position: fixed;
    top: -100px;
    width: 50vw;
    place-items: start;
    position: fixed;
    top: -100px;
    width: 70vw;
    font-size: 18px !important;
  }

  .mobile-nav-language-selector .v-image {
    margin-right: 10px;
  }

  .main-menu-link-mob-nav {
    text-decoration: none;
    color: black !important;
    font-weight: bold;
    margin: 10px 0px 10px 45px;
  }

  #logout-button-in-modal {
    text-decoration: none;
    color: black;
  }

  .first-name {
    font-weight: bold;
  }

  .on-avatar-click-menu {
    display: block;
    background-color: #fffaeb;
    opacity: 0.8;
  }

  .on-click-avatar-submenu {
    text-decoration: none;
    padding: 0px;
    margin: 5px 0px;
    color: black !important;
  }

  .on-click-avatar-submenu:hover {
    color: #fbb03b;
  }

  .on-avatar-click-menu,
  .v-list-item {
    min-height: 30px !important;
    padding-left: 15px !important;
    font-weight: bold;
    font-size: 16px;
    padding: 10px;
  }

  .main-menu-is-teacher .main-dropdown {
    &-menu {
      top: 60px !important;
    }
  }

  .nav-bar-teacher-items {
    justify-content: center;
    place-items: center;
    margin-right: -40vw;
  }

  .header--light .nav-bar-teacher-items svg {
    fill: black !important;
  }

  .header--dark .nav-bar-teacher-items svg {
    fill: white !important;
  }

  #avatar-down-arrow svg {
    fill: black !important;
  }

  #find-teacher-search-icon-svg-dark svg {
    fill: black !important;
  }

  #find-teacher-search-icon-svg-dark {
    display: flex;
  }

  .main-menu-link-mob-nav .item-icon {
    height: 20px;
    display: flex;
    justify-content: center;
    place-items: center;
    margin-left: -25px;
  }
  .search-icon {
    width: 20px;
    margin-top: 45px;
    margin-left: -120px;
  }
  .search-icon:hover {
    fill: black;
  }

  .hightlighted-teacher-listing::before {
    content: '';
    position: absolute;
    left: 0;
    height: 35px;
    width: 5px;
    background-color: #fbb03b;
    border-radius: 10px;
    margin-top: 15px;
  }

  .hightlighted-nav-bar-item::before {
    content: '';
    position: absolute;
    left: 0;
    height: 35px;
    width: 5px;
    background-color: #fbb03b;
    border-radius: 10px;
    margin-top: -5px;
    margin-right: -5px;
  }

  .close-button-nav-bar {
    width: 100%;
    display: flex;
    justify-content: flex-end;
    margin-top: 20px;
    padding-right: 20px;
  }
  .close-button-nav-bar svg {
    fill: black;
  }
}

.v-navigation-drawer--temporary {
  z-index: 102 !important;
}
</style>
