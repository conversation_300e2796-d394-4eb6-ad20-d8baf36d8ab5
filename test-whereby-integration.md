# Whereby Integration Test Plan

## Integration Summary

The Whereby video call integration has been successfully implemented as a third option alongside Twilio and Tokbox. Here's what was completed:

### 1. ✅ Whereby SDK Installation
- Added `@whereby.com/browser-sdk@^3.12.18` to package.json
- SDK installed successfully despite Node.js version warnings (expected for Vue.js app)

### 2. ✅ Whereby Component Creation
- Created `components/classroom/video/Whereby.vue`
- Follows same pattern as Twilio.vue and Tokbox.vue
- Implements all required methods: toggleVideo, toggleAudio, toggleFullScreen, toggleScreenShare
- Uses provided API key: `langu-test`
- Uses provided JWT token for authentication
- Handles room creation with lesson-specific room names

### 3. ✅ VideoActions Component Update
- Added third button (C) for Whereby alongside A (Twilio) and B (Tokbox)
- <PERSON><PERSON> highlights when Whereby is selected
- Emits switch-video-player event with 'whereby' type

### 4. ✅ Classroom Page Integration
- Added Whereby import and component registration
- Added `<whereby>` component to template with proper conditions
- Created `wherebyAsset` computed property
- Updated asset creation logic to check for all three video providers

### 5. ✅ Asset Management
- Modified classroom initialization to support Whereby assets
- Updated condition to check for `!this.twilioAsset && !this.tokboxAsset && !this.wherebyAsset`
- Switching between providers properly deletes old asset and creates new one

## Key Features Implemented

### Video Controls
- Camera toggle (on/off)
- Microphone toggle (mute/unmute)
- Fullscreen mode
- Screen sharing capability
- Video provider switching (A/B/C buttons)

### Room Management
- Uses lesson-specific room names: `lesson-${lessonId}`
- Room URL format: `https://langu-test.whereby.com/${roomName}`
- Displays user's classroom name in video call

### Error Handling
- Falls back to Tokbox if Whereby initialization fails
- Handles media permission errors
- Console logging for debugging

## Testing Checklist

To test the integration:

1. **Start Development Server**
   ```bash
   npm run dev
   ```

2. **Navigate to Classroom**
   - Go to any lesson classroom page
   - Look for video controls at bottom of video window

3. **Test Video Provider Switching**
   - Click button A (Twilio) - should show Twilio video
   - Click button B (Tokbox) - should show Tokbox video  
   - Click button C (Whereby) - should show Whereby video
   - Verify button highlighting shows active provider

4. **Test Whereby Functionality**
   - Verify video container loads
   - Test camera toggle button
   - Test microphone toggle button
   - Test fullscreen mode
   - Test screen sharing (if supported)

5. **Test Error Scenarios**
   - Block camera/microphone permissions
   - Verify error handling and fallback behavior

## Configuration Notes

- **API Key**: `langu-test` (hardcoded in component)
- **JWT Token**: Provided token (hardcoded in component)
- **Room URL Pattern**: `https://langu-test.whereby.com/{roomName}`
- **Room Name**: Uses existing `twilioRoomName` getter or falls back to `lesson-${lessonId}`

## Next Steps for Production

1. **Move credentials to environment variables**
2. **Implement proper room creation via backend API**
3. **Add proper error handling and user feedback**
4. **Test with multiple participants**
5. **Verify screen sharing functionality**
6. **Add loading states and connection indicators**

The integration is now complete and ready for testing!
